# Authentication Service Configuration

# Service Configuration
LEG<PERSON><PERSON><PERSON>ID<PERSON>_SERVICE_NAME=auth-service
LEGACYBRIDGE_SERVICE_VERSION=0.1.0
LEGACYBRIDGE_SERVICE_PORT=3001
LEGACYBRIDGE_SERVICE_HOST=0.0.0.0
LEG<PERSON><PERSON><PERSON>ID<PERSON>_SERVICE_ENVIRONMENT=development

# Database Configuration
LEGACYBRIDGE_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/legacybridge
LEGACYBRIDGE_DATABASE_MAX_CONNECTIONS=20
LEGAC<PERSON><PERSON>IDGE_DATABASE_MIN_CONNECTIONS=5
LEGACYBR<PERSON>GE_DATABASE_ACQUIRE_TIMEOUT_SECONDS=10
LEGACYBRIDGE_DATABASE_IDLE_TIMEOUT_SECONDS=600
LEGACYBRIDGE_DATABASE_MAX_LIFETIME_SECONDS=1800

# Redis Configuration
LEGACYBRIDGE_REDIS_URL=redis://localhost:6379
LEG<PERSON><PERSON><PERSON><PERSON>GE_REDIS_MAX_CONNECTIONS=10
LEGAC<PERSON><PERSON><PERSON><PERSON>_REDIS_CONNECTION_TIMEOUT_SECONDS=5
LEGACYBRIDGE_REDIS_COMMAND_TIMEOUT_SECONDS=5

# Authentication Configuration
LEGACYBRIDGE_AUTH_JWT_SECRET=your-super-secret-jwt-key-change-in-production
LEGACYBRIDGE_AUTH_JWT_EXPIRY_HOURS=1
LEGACYBRIDGE_AUTH_REFRESH_TOKEN_EXPIRY_DAYS=7
LEGACYBRIDGE_AUTH_ISSUER=legacybridge-auth

# Logging Configuration
LEGACYBRIDGE_LOGGING_LEVEL=info
LEGACYBRIDGE_LOGGING_FORMAT=json

# Metrics Configuration
LEGACYBRIDGE_METRICS_ENABLED=true
LEGACYBRIDGE_METRICS_PORT=9091
LEGACYBRIDGE_METRICS_PATH=/metrics

# CORS Configuration
LEGACYBRIDGE_CORS_ALLOWED_ORIGINS=*
LEGACYBRIDGE_CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
LEGACYBRIDGE_CORS_ALLOWED_HEADERS=Content-Type,Authorization
LEGACYBRIDGE_CORS_MAX_AGE_SECONDS=3600

# Rate Limiting Configuration
LEGACYBRIDGE_RATE_LIMITING_ENABLED=true
LEGACYBRIDGE_RATE_LIMITING_REQUESTS_PER_MINUTE=100
LEGACYBRIDGE_RATE_LIMITING_BURST_SIZE=10

# Tracing Configuration
JAEGER_AGENT_ENDPOINT=http://localhost:14268/api/traces
