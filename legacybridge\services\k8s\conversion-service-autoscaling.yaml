# Conversion Service Auto-Scaling Configuration
# Horizontal and Vertical Pod Autoscalers for the Document Conversion Service

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: conversion-service-hpa
  namespace: legacybridge
  labels:
    app: conversion-service
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversion-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  # CPU-based scaling (conversion is CPU intensive)
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  # Memory-based scaling (large documents require memory)
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  # Custom metrics for conversion service
  - type: Pods
    pods:
      metric:
        name: conversion_queue_length
      target:
        type: AverageValue
        averageValue: "5"
  - type: Pods
    pods:
      metric:
        name: active_conversions
      target:
        type: AverageValue
        averageValue: "3"
  - type: Pods
    pods:
      metric:
        name: conversion_requests_per_second
      target:
        type: AverageValue
        averageValue: "10"
  # Average conversion time (scale up if conversions are taking too long)
  - type: Pods
    pods:
      metric:
        name: average_conversion_duration_seconds
      target:
        type: AverageValue
        averageValue: "30"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 3
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 20
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min

---
# Vertical Pod Autoscaler for Conversion Service
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: conversion-service-vpa
  namespace: legacybridge
  labels:
    app: conversion-service
    component: autoscaling
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversion-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: conversion-service
      minAllowed:
        cpu: 200m
        memory: 256Mi
      maxAllowed:
        cpu: 4000m
        memory: 8Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

---
# Pod Disruption Budget for Conversion Service
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: conversion-service-pdb
  namespace: legacybridge
  labels:
    app: conversion-service
    component: availability
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: conversion-service

---
# ServiceMonitor for Conversion Service Metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: conversion-service-metrics
  namespace: legacybridge
  labels:
    app: conversion-service
    component: monitoring
spec:
  selector:
    matchLabels:
      app: conversion-service
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    honorLabels: true

---
# Custom Metrics Configuration for Conversion Service
apiVersion: v1
kind: ConfigMap
metadata:
  name: conversion-service-metrics-config
  namespace: legacybridge
  labels:
    app: conversion-service
    component: metrics
data:
  metrics.yaml: |
    metrics:
      conversion_queue_length:
        description: "Number of conversion jobs in queue"
        type: "gauge"
      active_conversions:
        description: "Number of currently processing conversions"
        type: "gauge"
      conversion_requests_per_second:
        description: "Number of conversion requests per second"
        type: "gauge"
        labels: ["input_format", "output_format"]
      conversion_duration_seconds:
        description: "Time taken to complete conversions"
        type: "histogram"
        labels: ["input_format", "output_format", "status"]
        buckets: [0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0]
      conversion_success_rate:
        description: "Conversion success rate"
        type: "gauge"
        labels: ["input_format", "output_format"]
      conversion_file_size_bytes:
        description: "Size of files being converted"
        type: "histogram"
        buckets: [1024, 10240, 102400, 1048576, 10485760, 104857600]
      redis_queue_operations:
        description: "Redis queue operations"
        type: "counter"
        labels: ["operation", "status"]

---
# Prometheus Rules for Conversion Service
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: conversion-service-alerts
  namespace: legacybridge
  labels:
    app: conversion-service
    component: alerting
spec:
  groups:
  - name: conversion-service.rules
    rules:
    # High queue length alert
    - alert: ConversionServiceHighQueueLength
      expr: conversion_queue_length > 50
      for: 2m
      labels:
        severity: warning
        service: conversion-service
      annotations:
        summary: "Conversion service queue is backing up"
        description: "Conversion queue has {{ $value }} pending jobs"
    
    # High error rate alert
    - alert: ConversionServiceHighErrorRate
      expr: rate(conversion_requests_total{status=~"failed|error"}[5m]) / rate(conversion_requests_total[5m]) > 0.1
      for: 5m
      labels:
        severity: critical
        service: conversion-service
      annotations:
        summary: "Conversion service high error rate"
        description: "Conversion service error rate is {{ $value | humanizePercentage }}"
    
    # Slow conversion alert
    - alert: ConversionServiceSlowConversions
      expr: histogram_quantile(0.95, rate(conversion_duration_seconds_bucket[5m])) > 120
      for: 5m
      labels:
        severity: warning
        service: conversion-service
      annotations:
        summary: "Conversion service slow conversions"
        description: "95th percentile conversion time is {{ $value }}s"
    
    # High CPU usage alert
    - alert: ConversionServiceHighCPU
      expr: rate(container_cpu_usage_seconds_total{pod=~"conversion-service-.*"}[5m]) > 0.8
      for: 3m
      labels:
        severity: warning
        service: conversion-service
      annotations:
        summary: "Conversion service high CPU usage"
        description: "Conversion service CPU usage is {{ $value | humanizePercentage }}"
    
    # Memory usage alert
    - alert: ConversionServiceHighMemory
      expr: container_memory_usage_bytes{pod=~"conversion-service-.*"} / container_spec_memory_limit_bytes > 0.9
      for: 2m
      labels:
        severity: warning
        service: conversion-service
      annotations:
        summary: "Conversion service high memory usage"
        description: "Conversion service memory usage is {{ $value | humanizePercentage }}"
    
    # Service down alert
    - alert: ConversionServiceDown
      expr: up{job="conversion-service"} == 0
      for: 1m
      labels:
        severity: critical
        service: conversion-service
      annotations:
        summary: "Conversion service is down"
        description: "Conversion service has been down for more than 1 minute"
    
    # Redis connection issues
    - alert: ConversionServiceRedisConnectionIssues
      expr: rate(redis_queue_operations_total{status="error"}[5m]) > 0.1
      for: 2m
      labels:
        severity: warning
        service: conversion-service
      annotations:
        summary: "Conversion service Redis connection issues"
        description: "Conversion service is experiencing Redis connection errors"

---
# Network Policy for Conversion Service
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: conversion-service-network-policy
  namespace: legacybridge
  labels:
    app: conversion-service
    component: security
spec:
  podSelector:
    matchLabels:
      app: conversion-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from Kong Gateway
  - from:
    - podSelector:
        matchLabels:
          app: kong-gateway
    ports:
    - protocol: TCP
      port: 3002
  # Allow traffic from other microservices
  - from:
    - podSelector:
        matchLabels:
          app: auth-service
    - podSelector:
        matchLabels:
          app: file-service
    - podSelector:
        matchLabels:
          app: job-service
    ports:
    - protocol: TCP
      port: 3002
  # Allow monitoring traffic
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  egress:
  # Allow traffic to Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  # Allow traffic to File Service for file operations
  - to:
    - podSelector:
        matchLabels:
          app: file-service
    ports:
    - protocol: TCP
      port: 3003
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
