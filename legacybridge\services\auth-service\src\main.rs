// Authentication Service for LegacyBridge
// Handles user authentication, JWT token management, and user management

mod handlers;
mod middleware;
mod service;

use axum::{
    extract::Extension,
    http::{HeaderValue, Method},
    routing::{get, post, put, delete},
    Router,
};
use legacybridge_shared::{
    config::ServiceConfig,
    database::DatabaseManager,
    cache::CacheManager,
    metrics::ServiceMetrics,
    events::EventPublisher,
    auth::JwtManager,
    ServiceResult,
};
use prometheus::Registry;
use std::{sync::Arc, time::Duration};
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
    timeout::TimeoutLayer,
    request_id::{MakeRequestUuid, PropagateRequestIdLayer, SetRequestIdLayer},
};
use tracing::{info, error};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

// Application state
#[derive(Clone)]
pub struct AppState {
    pub db: Arc<DatabaseManager>,
    pub cache: Arc<CacheManager>,
    pub jwt_manager: Arc<JwtManager>,
    pub metrics: Arc<ServiceMetrics>,
    pub event_publisher: Arc<EventPublisher>,
    pub config: Arc<ServiceConfig>,
}

#[tokio::main]
async fn main() -> ServiceResult<()> {
    // Load configuration
    dotenvy::dotenv().ok();
    let config = Arc::new(ServiceConfig::auth_service_config());
    config.validate().map_err(|e| legacybridge_shared::ServiceError::Configuration(e))?;

    // Initialize tracing
    init_tracing(&config.service.name)?;

    info!(
        service = %config.service.name,
        version = %config.service.version,
        port = %config.service.port,
        "Starting authentication service"
    );

    // Initialize database
    let db = Arc::new(DatabaseManager::new(&config.database.url).await?);
    info!("Database connection established");

    // Initialize cache
    let cache = Arc::new(CacheManager::new(
        &config.redis.url,
        Duration::from_secs(3600),
    )?);
    info!("Redis cache connection established");

    // Initialize JWT manager
    let jwt_manager = Arc::new(JwtManager::new(
        &config.auth.jwt_secret,
        config.auth.issuer.clone(),
    ));

    // Initialize metrics
    let registry = Registry::new();
    let metrics = Arc::new(ServiceMetrics::new(&config.service.name, &registry)?);

    // Initialize event publisher
    let event_publisher = Arc::new(EventPublisher::new(&config.redis.url)?);

    // Create application state
    let state = AppState {
        db,
        cache,
        jwt_manager,
        metrics,
        event_publisher,
        config: config.clone(),
    };

    // Build the application router
    let app = create_router(state.clone()).await?;

    // Start metrics server
    let metrics_app = Router::new()
        .route("/metrics", get(|| async move {
            use prometheus::Encoder;
            let encoder = prometheus::TextEncoder::new();
            let metric_families = registry.gather();
            match encoder.encode_to_string(&metric_families) {
                Ok(output) => output,
                Err(e) => {
                    error!("Failed to encode metrics: {}", e);
                    String::new()
                }
            }
        }))
        .route("/health", get(handlers::health::health_check))
        .layer(Extension(state.clone()));

    // Start metrics server in background
    let metrics_port = config.metrics.port;
    tokio::spawn(async move {
        let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", metrics_port))
            .await
            .expect("Failed to bind metrics server");
        
        info!("Metrics server listening on port {}", metrics_port);
        
        axum::serve(listener, metrics_app)
            .await
            .expect("Metrics server failed");
    });

    // Start main application server
    let listener = tokio::net::TcpListener::bind(format!("{}:{}", config.service.host, config.service.port))
        .await
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Failed to bind server: {}", e)))?;

    info!(
        "Authentication service listening on {}:{}",
        config.service.host, config.service.port
    );

    axum::serve(listener, app)
        .await
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Server error: {}", e)))?;

    Ok(())
}

async fn create_router(state: AppState) -> ServiceResult<Router> {
    // CORS configuration
    let cors = CorsLayer::new()
        .allow_origin("*".parse::<HeaderValue>().unwrap())
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE, Method::OPTIONS])
        .allow_headers(tower_http::cors::Any);

    // Request ID layer
    let request_id_layer = ServiceBuilder::new()
        .layer(SetRequestIdLayer::x_request_id(MakeRequestUuid))
        .layer(PropagateRequestIdLayer::x_request_id());

    // Build the router
    let router = Router::new()
        // Authentication routes
        .route("/api/v1/auth/login", post(handlers::auth::login))
        .route("/api/v1/auth/refresh", post(handlers::auth::refresh_token))
        .route("/api/v1/auth/logout", post(handlers::auth::logout))
        .route("/api/v1/auth/validate", post(handlers::auth::validate_token))
        
        // User management routes
        .route("/api/v1/users", get(handlers::users::list_users))
        .route("/api/v1/users", post(handlers::users::create_user))
        .route("/api/v1/users/:id", get(handlers::users::get_user))
        .route("/api/v1/users/:id", put(handlers::users::update_user))
        .route("/api/v1/users/:id", delete(handlers::users::delete_user))
        
        // Profile management
        .route("/api/v1/profile", get(handlers::users::get_profile))
        .route("/api/v1/profile", put(handlers::users::update_profile))
        .route("/api/v1/profile/password", put(handlers::users::change_password))
        
        // Health and status
        .route("/health", get(handlers::health::health_check))
        .route("/ready", get(handlers::health::readiness_check))
        .route("/status", get(handlers::health::status))
        
        // Add middleware
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(TimeoutLayer::new(Duration::from_secs(30)))
                .layer(cors)
                .layer(request_id_layer)
                .layer(middleware::metrics::MetricsMiddleware::new())
                .layer(Extension(state))
        );

    Ok(router)
}

fn init_tracing(service_name: &str) -> ServiceResult<()> {
    // Initialize Jaeger tracer
    let tracer = opentelemetry_jaeger::new_agent_pipeline()
        .with_service_name(service_name)
        .install_simple()
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Failed to initialize tracer: {}", e)))?;

    let opentelemetry = tracing_opentelemetry::layer().with_tracer(tracer);

    // Initialize tracing subscriber
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "auth_service=debug,tower_http=debug,axum::rejection=trace".into()),
        )
        .with(tracing_subscriber::fmt::layer().json())
        .with(opentelemetry)
        .init();

    Ok(())
}
