#!/bin/bash

# Kong configuration validation script for LegacyBridge
set -e

KONG_ADMIN_URL="http://localhost:8001"
KONG_PROXY_URL="http://localhost:8000"

echo "🔍 Validating Kong API Gateway configuration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if Kong is responding
check_kong_health() {
    echo "🏥 Checking Kong health..."
    
    if curl -f $KONG_ADMIN_URL/status > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Kong Admin API is healthy${NC}"
    else
        echo -e "  ${RED}❌ Kong Admin API is not responding${NC}"
        return 1
    fi
    
    if curl -f $KONG_PROXY_URL > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Kong Proxy is healthy${NC}"
    else
        echo -e "  ${RED}❌ Kong Proxy is not responding${NC}"
        return 1
    fi
}

# Function to validate services
validate_services() {
    echo "🏗️  Validating services..."
    
    local services=("auth-service" "conversion-service" "file-service" "job-service")
    local all_services_ok=true
    
    for service in "${services[@]}"; do
        echo "  🔍 Checking service: $service"
        
        local service_info=$(curl -s $KONG_ADMIN_URL/services/$service 2>/dev/null)
        if echo "$service_info" | grep -q "\"name\":\"$service\""; then
            echo -e "    ${GREEN}✅ Service $service exists${NC}"
            
            # Check service configuration
            local host=$(echo "$service_info" | jq -r '.host' 2>/dev/null)
            local port=$(echo "$service_info" | jq -r '.port' 2>/dev/null)
            echo "    📍 Host: $host, Port: $port"
        else
            echo -e "    ${RED}❌ Service $service not found${NC}"
            all_services_ok=false
        fi
    done
    
    if [ "$all_services_ok" = true ]; then
        echo -e "  ${GREEN}✅ All services configured correctly${NC}"
    else
        echo -e "  ${RED}❌ Some services are missing or misconfigured${NC}"
        return 1
    fi
}

# Function to validate routes
validate_routes() {
    echo "🛣️  Validating routes..."
    
    local routes=("auth-route" "conversion-route" "file-route" "job-route")
    local all_routes_ok=true
    
    for route in "${routes[@]}"; do
        echo "  🔍 Checking route: $route"
        
        local route_info=$(curl -s $KONG_ADMIN_URL/routes/$route 2>/dev/null)
        if echo "$route_info" | grep -q "\"name\":\"$route\""; then
            echo -e "    ${GREEN}✅ Route $route exists${NC}"
            
            # Check route configuration
            local paths=$(echo "$route_info" | jq -r '.paths[]' 2>/dev/null | tr '\n' ' ')
            local methods=$(echo "$route_info" | jq -r '.methods[]' 2>/dev/null | tr '\n' ' ')
            echo "    📍 Paths: [$paths], Methods: [$methods]"
        else
            echo -e "    ${RED}❌ Route $route not found${NC}"
            all_routes_ok=false
        fi
    done
    
    if [ "$all_routes_ok" = true ]; then
        echo -e "  ${GREEN}✅ All routes configured correctly${NC}"
    else
        echo -e "  ${RED}❌ Some routes are missing or misconfigured${NC}"
        return 1
    fi
}

# Function to validate plugins
validate_plugins() {
    echo "🔌 Validating plugins..."
    
    local plugins_info=$(curl -s $KONG_ADMIN_URL/plugins 2>/dev/null)
    local plugin_count=$(echo "$plugins_info" | jq '.total' 2>/dev/null || echo "0")
    
    echo "  📊 Total plugins configured: $plugin_count"
    
    # Check for specific plugins
    local expected_plugins=("legacybridge-auth" "legacybridge-rate-limit" "legacybridge-transformer" "prometheus")
    local plugins_ok=true
    
    for plugin in "${expected_plugins[@]}"; do
        if echo "$plugins_info" | grep -q "\"name\":\"$plugin\""; then
            echo -e "    ${GREEN}✅ Plugin $plugin is active${NC}"
        else
            echo -e "    ${YELLOW}⚠️  Plugin $plugin not found${NC}"
            plugins_ok=false
        fi
    done
    
    if [ "$plugins_ok" = true ]; then
        echo -e "  ${GREEN}✅ All expected plugins are configured${NC}"
    else
        echo -e "  ${YELLOW}⚠️  Some plugins may be missing${NC}"
    fi
}

# Function to validate upstreams
validate_upstreams() {
    echo "⚖️  Validating upstreams..."
    
    local upstreams=("auth-service-upstream" "conversion-service-upstream" "file-service-upstream" "job-service-upstream")
    local all_upstreams_ok=true
    
    for upstream in "${upstreams[@]}"; do
        echo "  🔍 Checking upstream: $upstream"
        
        local upstream_info=$(curl -s $KONG_ADMIN_URL/upstreams/$upstream 2>/dev/null)
        if echo "$upstream_info" | grep -q "\"name\":\"$upstream\""; then
            echo -e "    ${GREEN}✅ Upstream $upstream exists${NC}"
            
            # Check targets
            local targets_info=$(curl -s $KONG_ADMIN_URL/upstreams/$upstream/targets 2>/dev/null)
            local target_count=$(echo "$targets_info" | jq '.total' 2>/dev/null || echo "0")
            echo "    🎯 Targets: $target_count"
            
            # Check health
            local health_info=$(curl -s $KONG_ADMIN_URL/upstreams/$upstream/health 2>/dev/null)
            local healthy_targets=$(echo "$health_info" | jq '[.data[] | select(.health == "HEALTHY")] | length' 2>/dev/null || echo "0")
            echo "    🏥 Healthy targets: $healthy_targets/$target_count"
        else
            echo -e "    ${RED}❌ Upstream $upstream not found${NC}"
            all_upstreams_ok=false
        fi
    done
    
    if [ "$all_upstreams_ok" = true ]; then
        echo -e "  ${GREEN}✅ All upstreams configured correctly${NC}"
    else
        echo -e "  ${RED}❌ Some upstreams are missing or misconfigured${NC}"
        return 1
    fi
}

# Function to test API endpoints
test_endpoints() {
    echo "🧪 Testing API endpoints..."
    
    # Test health endpoints (should be accessible without auth)
    local health_endpoints=("/auth/health" "/convert/health" "/files/health" "/jobs/health")
    local endpoints_ok=true
    
    for endpoint in "${health_endpoints[@]}"; do
        echo "  🔍 Testing endpoint: $endpoint"
        
        local response=$(curl -s -o /dev/null -w "%{http_code}" $KONG_PROXY_URL$endpoint 2>/dev/null)
        if [ "$response" = "200" ]; then
            echo -e "    ${GREEN}✅ Endpoint $endpoint responding (HTTP $response)${NC}"
        else
            echo -e "    ${RED}❌ Endpoint $endpoint failed (HTTP $response)${NC}"
            endpoints_ok=false
        fi
    done
    
    if [ "$endpoints_ok" = true ]; then
        echo -e "  ${GREEN}✅ All health endpoints are responding${NC}"
    else
        echo -e "  ${RED}❌ Some endpoints are not responding${NC}"
        return 1
    fi
}

# Function to check metrics
check_metrics() {
    echo "📊 Checking metrics..."
    
    local metrics_response=$(curl -s $KONG_ADMIN_URL/metrics 2>/dev/null)
    if echo "$metrics_response" | grep -q "kong_"; then
        echo -e "  ${GREEN}✅ Prometheus metrics are available${NC}"
        
        # Count metrics
        local metric_count=$(echo "$metrics_response" | grep -c "^kong_" || echo "0")
        echo "    📈 Available metrics: $metric_count"
    else
        echo -e "  ${YELLOW}⚠️  Prometheus metrics not available${NC}"
    fi
}

# Main validation flow
main() {
    echo "🚀 Starting Kong configuration validation..."
    echo ""
    
    local validation_failed=false
    
    # Run all validations
    check_kong_health || validation_failed=true
    echo ""
    
    validate_services || validation_failed=true
    echo ""
    
    validate_routes || validation_failed=true
    echo ""
    
    validate_plugins
    echo ""
    
    validate_upstreams || validation_failed=true
    echo ""
    
    test_endpoints || validation_failed=true
    echo ""
    
    check_metrics
    echo ""
    
    # Final result
    if [ "$validation_failed" = true ]; then
        echo -e "${RED}❌ Kong configuration validation FAILED${NC}"
        echo "Please check the errors above and run the setup scripts again."
        exit 1
    else
        echo -e "${GREEN}✅ Kong configuration validation PASSED${NC}"
        echo "All services, routes, and plugins are configured correctly!"
        echo ""
        echo "🎯 API Gateway is ready for production traffic!"
    fi
}

# Run main function
main
