// HTML parsing and generation
use super::{ParsedDocument, DocumentElement};
use legacybridge_shared::{ServiceError, ServiceResult};
use serde_json::Value;
use std::collections::HashMap;

/// Parse HTML content into structured document
pub fn parse_html(content: &str) -> ServiceResult<ParsedDocument> {
    // For now, use html2text to convert HTML to plain text
    // In a full implementation, you'd use a proper HTML parser like scraper or html5ever
    let text_content = html2text::from_reader(content.as_bytes(), 80);
    
    // Parse the resulting text as plain text
    let mut text_doc = super::text::parse_text(&text_content)?;
    
    // Update metadata to indicate HTML origin
    text_doc.metadata.insert("original_format".to_string(), Value::String("html".to_string()));
    text_doc.metadata.insert("format".to_string(), Value::String("html_parsed".to_string()));
    
    Ok(text_doc)
}

/// Generate HTML from structured document
pub fn generate_html(document: &ParsedDocument, options: Option<&Value>) -> ServiceResult<String> {
    let mut output = String::new();
    
    // Check options for styling preferences
    let include_css = options
        .and_then(|opts| opts.get("include_css"))
        .and_then(|css| css.as_bool())
        .unwrap_or(true);
    
    let css_framework = options
        .and_then(|opts| opts.get("css_framework"))
        .and_then(|fw| fw.as_str())
        .unwrap_or("basic");

    // HTML document structure
    output.push_str("<!DOCTYPE html>\n");
    output.push_str("<html lang=\"en\">\n");
    output.push_str("<head>\n");
    output.push_str("    <meta charset=\"UTF-8\">\n");
    output.push_str("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
    
    // Title
    if let Some(title) = &document.title {
        output.push_str(&format!("    <title>{}</title>\n", escape_html(title)));
    } else {
        output.push_str("    <title>Converted Document</title>\n");
    }
    
    // CSS
    if include_css {
        output.push_str("    <style>\n");
        output.push_str(&get_css_styles(css_framework));
        output.push_str("    </style>\n");
    }
    
    output.push_str("</head>\n");
    output.push_str("<body>\n");
    
    // Document content
    for element in &document.structure {
        match element {
            DocumentElement::Heading { level, text } => {
                let tag = format!("h{}", level.min(&6));
                output.push_str(&format!("    <{0}>{1}</{0}>\n", tag, escape_html(text)));
            }
            DocumentElement::Paragraph { text } => {
                output.push_str(&format!("    <p>{}</p>\n", escape_html(text)));
            }
            DocumentElement::List { items, ordered } => {
                let tag = if *ordered { "ol" } else { "ul" };
                output.push_str(&format!("    <{}>\n", tag));
                for item in items {
                    output.push_str(&format!("        <li>{}</li>\n", escape_html(item)));
                }
                output.push_str(&format!("    </{}>\n", tag));
            }
            DocumentElement::Code { text, language } => {
                if let Some(lang) = language {
                    output.push_str(&format!("    <pre><code class=\"language-{}\">{}</code></pre>\n", 
                        escape_html(lang), escape_html(text)));
                } else {
                    output.push_str(&format!("    <pre><code>{}</code></pre>\n", escape_html(text)));
                }
            }
            DocumentElement::Quote { text } => {
                output.push_str(&format!("    <blockquote>{}</blockquote>\n", escape_html(text)));
            }
            DocumentElement::Table { headers, rows } => {
                output.push_str("    <table>\n");
                
                if !headers.is_empty() {
                    output.push_str("        <thead>\n");
                    output.push_str("            <tr>\n");
                    for header in headers {
                        output.push_str(&format!("                <th>{}</th>\n", escape_html(header)));
                    }
                    output.push_str("            </tr>\n");
                    output.push_str("        </thead>\n");
                }
                
                if !rows.is_empty() {
                    output.push_str("        <tbody>\n");
                    for row in rows {
                        output.push_str("            <tr>\n");
                        for cell in row {
                            output.push_str(&format!("                <td>{}</td>\n", escape_html(cell)));
                        }
                        output.push_str("            </tr>\n");
                    }
                    output.push_str("        </tbody>\n");
                }
                
                output.push_str("    </table>\n");
            }
            DocumentElement::Image { alt, url } => {
                output.push_str(&format!("    <img src=\"{}\" alt=\"{}\" />\n", 
                    escape_html(url), escape_html(alt)));
            }
            DocumentElement::Link { text, url } => {
                output.push_str(&format!("<a href=\"{}\">{}</a>", 
                    escape_html(url), escape_html(text)));
            }
        }
    }
    
    output.push_str("</body>\n");
    output.push_str("</html>\n");
    
    Ok(output)
}

/// Escape HTML special characters
fn escape_html(text: &str) -> String {
    text.replace('&', "&amp;")
        .replace('<', "&lt;")
        .replace('>', "&gt;")
        .replace('"', "&quot;")
        .replace('\'', "&#x27;")
}

/// Get CSS styles based on framework choice
fn get_css_styles(framework: &str) -> String {
    match framework {
        "minimal" => {
            r#"
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        "#.to_string()
        }
        "github" => {
            r#"
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
            background-color: #fff;
            color: #24292e;
        }
        h1, h2, h3, h4, h5, h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        h1 { font-size: 2em; border-bottom: 1px solid #eaecef; padding-bottom: 10px; }
        h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 8px; }
        p { margin-bottom: 16px; }
        code {
            background-color: rgba(27,31,35,0.05);
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
        }
        blockquote {
            border-left: 4px solid #dfe2e5;
            margin: 0;
            padding: 0 16px;
            color: #6a737d;
        }
        table {
            border-collapse: collapse;
            border-spacing: 0;
            width: 100%;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 6px 13px;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        "#.to_string()
        }
        _ => {
            // Basic styles
            r#"
        body {
            font-family: Georgia, 'Times New Roman', serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        p {
            margin-bottom: 1em;
            text-align: justify;
        }
        code {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            padding: 2px 4px;
        }
        pre {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow-x: auto;
            padding: 10px;
        }
        blockquote {
            border-left: 4px solid #3498db;
            margin: 0;
            padding-left: 20px;
            font-style: italic;
            color: #555;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        ul, ol {
            padding-left: 30px;
        }
        li {
            margin-bottom: 0.5em;
        }
        "#.to_string()
        }
    }
}
