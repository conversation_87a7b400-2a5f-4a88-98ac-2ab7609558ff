[package]
name = "conversion-service"
version = "0.1.0"
edition = "2021"
description = "Document conversion service for LegacyBridge microservices architecture"

[dependencies]
# Shared library
legacybridge-shared = { path = "../shared" }

# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "timeout", "request-id"] }
hyper = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json", "migrate"] }

# Redis for job queue
redis = { version = "0.24", features = ["tokio-comp", "connection-manager", "streams"] }

# UUID and time
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-opentelemetry = "0.21"
opentelemetry = "0.20"
opentelemetry-jaeger = "0.19"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Validation
validator = { version = "0.16", features = ["derive"] }

# Configuration
config = "0.13"
dotenvy = "0.15"

# Metrics
prometheus = "0.13"

# HTTP client for service communication
reqwest = { version = "0.11", features = ["json"] }

# Base64 encoding/decoding
base64 = "0.21"

# File handling
mime = "0.3"
mime_guess = "2.0"
tempfile = "3.0"

# Document conversion libraries (lightweight alternatives to Pandoc)
pulldown-cmark = "0.9"  # Markdown parsing
html2text = "0.6"       # HTML to text conversion
rtf-parser = "0.3"      # RTF parsing

# Text processing
regex = "1.0"
encoding_rs = "0.8"

[dev-dependencies]
tokio-test = "0.4"
testcontainers = "0.15"
axum-test = "14.0"
