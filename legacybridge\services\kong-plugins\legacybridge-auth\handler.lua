-- Custom Kong plugin for LegacyBridge authentication validation
-- This plugin validates JWT tokens with the auth service and sets user context

local jwt_decoder = require "resty.jwt"
local http = require "resty.http"
local cjson = require "cjson"

local LegacyBridgeAuthHandler = {}

LegacyBridgeAuthHandler.PRIORITY = 1000
LegacyBridgeAuthHandler.VERSION = "1.0.0"

local function get_token_from_header(authorization_header)
    if not authorization_header then
        return nil, "Missing authorization header"
    end
    
    local bearer_token = string.match(authorization_header, "Bearer%s+(.+)")
    if not bearer_token then
        return nil, "Invalid authorization format"
    end
    
    return bearer_token, nil
end

local function validate_token_with_auth_service(token, config)
    local httpc = http.new()
    httpc:set_timeout(config.auth_service_timeout or 5000)
    
    local auth_service_url = config.auth_service_url or "http://auth-service:3001"
    local validation_url = auth_service_url .. "/api/v1/auth/validate"
    
    local request_body = cjson.encode({token = token})
    
    local res, err = httpc:request_uri(validation_url, {
        method = "POST",
        body = request_body,
        headers = {
            ["Content-Type"] = "application/json",
            ["User-Agent"] = "Kong-LegacyBridge-Auth-Plugin/1.0.0"
        }
    })
    
    if not res then
        kong.log.err("Failed to connect to auth service: ", err)
        return nil, "Auth service unavailable"
    end
    
    if res.status ~= 200 then
        kong.log.warn("Auth service returned status: ", res.status, " body: ", res.body)
        return nil, "Token validation failed"
    end
    
    local validation_result, decode_err = cjson.decode(res.body)
    if not validation_result then
        kong.log.err("Failed to decode auth service response: ", decode_err)
        return nil, "Invalid auth service response"
    end
    
    return validation_result, nil
end

local function is_public_endpoint(path, config)
    local public_paths = config.public_paths or {
        "/api/v1/auth/login",
        "/api/v1/auth/refresh",
        "/health",
        "/ready",
        "/status",
        "/metrics"
    }
    
    for _, public_path in ipairs(public_paths) do
        if string.match(path, "^" .. public_path) then
            return true
        end
    end
    
    return false
end

local function check_permissions(user_roles, required_permissions, config)
    if not required_permissions or #required_permissions == 0 then
        return true
    end
    
    -- Admin role has access to everything
    for _, role in ipairs(user_roles) do
        if role == "admin" then
            return true
        end
    end
    
    -- Check if user has any of the required permissions
    for _, required_perm in ipairs(required_permissions) do
        for _, user_role in ipairs(user_roles) do
            if user_role == required_perm then
                return true
            end
        end
    end
    
    return false
end

function LegacyBridgeAuthHandler:access(config)
    local path = kong.request.get_path()
    local method = kong.request.get_method()
    
    -- Skip authentication for public endpoints
    if is_public_endpoint(path, config) then
        kong.log.debug("Skipping authentication for public endpoint: ", path)
        return
    end
    
    -- Get authorization header
    local authorization_header = kong.request.get_header("Authorization")
    local token, token_err = get_token_from_header(authorization_header)
    
    if not token then
        kong.log.warn("Token extraction failed: ", token_err)
        return kong.response.exit(401, {
            error = "unauthorized",
            message = "Missing or invalid authorization token"
        })
    end
    
    -- Validate token with auth service
    local validation_result, validation_err = validate_token_with_auth_service(token, config)
    
    if not validation_result then
        kong.log.warn("Token validation failed: ", validation_err)
        return kong.response.exit(401, {
            error = "unauthorized",
            message = "Token validation failed"
        })
    end
    
    if not validation_result.valid then
        kong.log.warn("Invalid token provided")
        return kong.response.exit(401, {
            error = "unauthorized",
            message = "Invalid or expired token"
        })
    end
    
    -- Check permissions if configured
    local required_permissions = config.required_permissions
    if required_permissions and not check_permissions(validation_result.roles or {}, required_permissions, config) then
        kong.log.warn("Insufficient permissions for user: ", validation_result.user_id)
        return kong.response.exit(403, {
            error = "forbidden",
            message = "Insufficient permissions"
        })
    end
    
    -- Set user context headers for downstream services
    kong.service.request.set_header("X-User-ID", validation_result.user_id)
    kong.service.request.set_header("X-User-Roles", table.concat(validation_result.roles or {}, ","))
    kong.service.request.set_header("X-User-Email", validation_result.email or "")
    kong.service.request.set_header("X-Auth-Time", tostring(ngx.time()))
    
    -- Add request tracking
    kong.service.request.set_header("X-Request-ID", kong.request.get_header("X-Request-ID") or kong.uuid())
    
    kong.log.info("Authentication successful for user: ", validation_result.user_id, " path: ", path)
end

function LegacyBridgeAuthHandler:header_filter(config)
    -- Add security headers
    kong.response.set_header("X-Content-Type-Options", "nosniff")
    kong.response.set_header("X-Frame-Options", "DENY")
    kong.response.set_header("X-XSS-Protection", "1; mode=block")
    
    -- Remove sensitive headers
    kong.response.clear_header("X-User-ID")
    kong.response.clear_header("X-User-Roles")
    kong.response.clear_header("X-User-Email")
end

return LegacyBridgeAuthHandler
