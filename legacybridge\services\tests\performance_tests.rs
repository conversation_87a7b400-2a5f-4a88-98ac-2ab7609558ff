// Comprehensive performance tests for LegacyBridge microservices
use reqwest::Client;
use serde_json::{json, Value};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::time::sleep;

const KONG_PROXY_URL: &str = "http://localhost:8000";
const TARGET_RPS: f64 = 1000.0; // Target: 1000+ requests per minute
const LOAD_TEST_DURATION: Duration = Duration::from_secs(60);
const CONCURRENT_USERS: usize = 50;

#[derive(Debug, <PERSON>lone)]
struct PerformanceMetrics {
    total_requests: usize,
    successful_requests: usize,
    failed_requests: usize,
    total_duration: Duration,
    min_response_time: Duration,
    max_response_time: Duration,
    avg_response_time: Duration,
    p95_response_time: Duration,
    p99_response_time: Duration,
    requests_per_second: f64,
    error_rate: f64,
}

impl PerformanceMetrics {
    fn new() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            total_duration: Duration::ZERO,
            min_response_time: Duration::MAX,
            max_response_time: Duration::ZERO,
            avg_response_time: Duration::ZERO,
            p95_response_time: Duration::ZERO,
            p99_response_time: Duration::ZERO,
            requests_per_second: 0.0,
            error_rate: 0.0,
        }
    }

    fn calculate_from_response_times(&mut self, response_times: &[Duration], total_duration: Duration) {
        if response_times.is_empty() {
            return;
        }

        let mut sorted_times = response_times.to_vec();
        sorted_times.sort();

        self.total_requests = response_times.len();
        self.total_duration = total_duration;
        self.min_response_time = sorted_times[0];
        self.max_response_time = sorted_times[sorted_times.len() - 1];
        
        let sum: Duration = sorted_times.iter().sum();
        self.avg_response_time = sum / sorted_times.len() as u32;
        
        let p95_index = (sorted_times.len() as f64 * 0.95) as usize;
        let p99_index = (sorted_times.len() as f64 * 0.99) as usize;
        
        self.p95_response_time = sorted_times[p95_index.min(sorted_times.len() - 1)];
        self.p99_response_time = sorted_times[p99_index.min(sorted_times.len() - 1)];
        
        self.requests_per_second = self.total_requests as f64 / total_duration.as_secs_f64();
        self.error_rate = self.failed_requests as f64 / self.total_requests as f64;
    }

    fn print_summary(&self, test_name: &str) {
        println!("\n📊 Performance Test Results: {}", test_name);
        println!("==========================================");
        println!("Total Requests: {}", self.total_requests);
        println!("Successful: {} ({:.1}%)", self.successful_requests, 
                (self.successful_requests as f64 / self.total_requests as f64) * 100.0);
        println!("Failed: {} ({:.1}%)", self.failed_requests, self.error_rate * 100.0);
        println!("Test Duration: {:?}", self.total_duration);
        println!("Requests/Second: {:.2}", self.requests_per_second);
        println!("Response Times:");
        println!("  Min: {:?}", self.min_response_time);
        println!("  Avg: {:?}", self.avg_response_time);
        println!("  Max: {:?}", self.max_response_time);
        println!("  95th percentile: {:?}", self.p95_response_time);
        println!("  99th percentile: {:?}", self.p99_response_time);
    }
}

async fn make_authenticated_request(
    client: &Client,
    method: &str,
    url: &str,
    payload: Option<&Value>,
    auth_token: Option<&str>,
) -> Result<(Duration, bool), Box<dyn std::error::Error + Send + Sync>> {
    let start = Instant::now();
    
    let mut request = match method {
        "GET" => client.get(url),
        "POST" => {
            let mut req = client.post(url);
            if let Some(p) = payload {
                req = req.json(p);
            }
            req
        }
        "PUT" => {
            let mut req = client.put(url);
            if let Some(p) = payload {
                req = req.json(p);
            }
            req
        }
        "DELETE" => client.delete(url),
        _ => return Err("Unsupported HTTP method".into()),
    };

    if let Some(token) = auth_token {
        request = request.header("Authorization", format!("Bearer {}", token));
    }

    let response = request.send().await?;
    let duration = start.elapsed();
    let success = response.status().is_success();

    Ok((duration, success))
}

#[tokio::test]
async fn test_auth_service_performance() {
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .build()
        .unwrap();

    let mut response_times = Vec::new();
    let mut successful_requests = 0;
    let mut failed_requests = 0;

    let test_start = Instant::now();
    let semaphore = Arc::new(Semaphore::new(CONCURRENT_USERS));

    let mut handles = vec![];

    // Generate load for the specified duration
    let end_time = test_start + LOAD_TEST_DURATION;
    let mut request_count = 0;

    while Instant::now() < end_time {
        let permit = semaphore.clone().acquire_owned().await.unwrap();
        let client_clone = client.clone();
        
        let handle = tokio::spawn(async move {
            let _permit = permit; // Keep permit until request completes
            
            // Test health endpoint
            let result = make_authenticated_request(
                &client_clone,
                "GET",
                &format!("{}/auth/health", KONG_PROXY_URL),
                None,
                None,
            ).await;

            match result {
                Ok((duration, success)) => (duration, success),
                Err(_) => (Duration::from_millis(30000), false), // Timeout as max duration
            }
        });

        handles.push(handle);
        request_count += 1;

        // Control request rate
        if request_count % 10 == 0 {
            sleep(Duration::from_millis(100)).await;
        }
    }

    // Wait for all requests to complete
    for handle in handles {
        let (duration, success) = handle.await.unwrap();
        response_times.push(duration);
        
        if success {
            successful_requests += 1;
        } else {
            failed_requests += 1;
        }
    }

    let total_duration = test_start.elapsed();

    let mut metrics = PerformanceMetrics::new();
    metrics.successful_requests = successful_requests;
    metrics.failed_requests = failed_requests;
    metrics.calculate_from_response_times(&response_times, total_duration);

    metrics.print_summary("Auth Service Load Test");

    // Performance assertions
    assert!(metrics.requests_per_second >= 10.0, "Auth service should handle at least 10 RPS");
    assert!(metrics.error_rate < 0.05, "Error rate should be less than 5%");
    assert!(metrics.p95_response_time < Duration::from_millis(2000), "95th percentile response time should be under 2s");
}

#[tokio::test]
async fn test_conversion_service_performance() {
    let client = Client::builder()
        .timeout(Duration::from_secs(60))
        .build()
        .unwrap();

    let conversion_payload = json!({
        "input_format": "txt",
        "output_format": "pdf",
        "file_content": "VGVzdCBjb250ZW50IGZvciBwZXJmb3JtYW5jZSB0ZXN0aW5n", // Base64 encoded
        "filename": "perf_test.txt"
    });

    let mut response_times = Vec::new();
    let mut successful_requests = 0;
    let mut failed_requests = 0;

    let test_start = Instant::now();
    let semaphore = Arc::new(Semaphore::new(10)); // Lower concurrency for conversion service

    let mut handles = vec![];

    // Run for shorter duration due to conversion complexity
    let test_duration = Duration::from_secs(30);
    let end_time = test_start + test_duration;

    while Instant::now() < end_time {
        let permit = semaphore.clone().acquire_owned().await.unwrap();
        let client_clone = client.clone();
        let payload_clone = conversion_payload.clone();
        
        let handle = tokio::spawn(async move {
            let _permit = permit;
            
            // Test conversion endpoint
            let result = make_authenticated_request(
                &client_clone,
                "POST",
                &format!("{}/convert", KONG_PROXY_URL),
                Some(&payload_clone),
                None,
            ).await;

            match result {
                Ok((duration, success)) => (duration, success),
                Err(_) => (Duration::from_millis(60000), false),
            }
        });

        handles.push(handle);
        
        // Slower rate for conversion requests
        sleep(Duration::from_millis(500)).await;
    }

    // Wait for all requests to complete
    for handle in handles {
        let (duration, success) = handle.await.unwrap();
        response_times.push(duration);
        
        if success {
            successful_requests += 1;
        } else {
            failed_requests += 1;
        }
    }

    let total_duration = test_start.elapsed();

    let mut metrics = PerformanceMetrics::new();
    metrics.successful_requests = successful_requests;
    metrics.failed_requests = failed_requests;
    metrics.calculate_from_response_times(&response_times, total_duration);

    metrics.print_summary("Conversion Service Load Test");

    // Performance assertions (more lenient for conversion service)
    assert!(metrics.requests_per_second >= 1.0, "Conversion service should handle at least 1 RPS");
    assert!(metrics.error_rate < 0.10, "Error rate should be less than 10%");
    assert!(metrics.p95_response_time < Duration::from_secs(30), "95th percentile response time should be under 30s");
}

#[tokio::test]
async fn test_file_service_performance() {
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .build()
        .unwrap();

    let mut response_times = Vec::new();
    let mut successful_requests = 0;
    let mut failed_requests = 0;

    let test_start = Instant::now();
    let semaphore = Arc::new(Semaphore::new(CONCURRENT_USERS));

    let mut handles = vec![];
    let test_duration = Duration::from_secs(30);
    let end_time = test_start + test_duration;

    while Instant::now() < end_time {
        let permit = semaphore.clone().acquire_owned().await.unwrap();
        let client_clone = client.clone();
        
        let handle = tokio::spawn(async move {
            let _permit = permit;
            
            // Test file service health endpoint
            let result = make_authenticated_request(
                &client_clone,
                "GET",
                &format!("{}/files/health", KONG_PROXY_URL),
                None,
                None,
            ).await;

            match result {
                Ok((duration, success)) => (duration, success),
                Err(_) => (Duration::from_millis(30000), false),
            }
        });

        handles.push(handle);
        
        sleep(Duration::from_millis(50)).await;
    }

    // Wait for all requests to complete
    for handle in handles {
        let (duration, success) = handle.await.unwrap();
        response_times.push(duration);
        
        if success {
            successful_requests += 1;
        } else {
            failed_requests += 1;
        }
    }

    let total_duration = test_start.elapsed();

    let mut metrics = PerformanceMetrics::new();
    metrics.successful_requests = successful_requests;
    metrics.failed_requests = failed_requests;
    metrics.calculate_from_response_times(&response_times, total_duration);

    metrics.print_summary("File Service Load Test");

    // Performance assertions
    assert!(metrics.requests_per_second >= 10.0, "File service should handle at least 10 RPS");
    assert!(metrics.error_rate < 0.05, "Error rate should be less than 5%");
    assert!(metrics.p95_response_time < Duration::from_millis(2000), "95th percentile response time should be under 2s");
}

#[tokio::test]
async fn test_end_to_end_workflow_performance() {
    let client = Client::builder()
        .timeout(Duration::from_secs(60))
        .build()
        .unwrap();

    let workflow_count = 10;
    let mut workflow_times = Vec::new();
    let mut successful_workflows = 0;

    for i in 0..workflow_count {
        let workflow_start = Instant::now();
        let mut workflow_success = true;

        // Step 1: Health check
        let health_result = make_authenticated_request(
            &client,
            "GET",
            &format!("{}/auth/health", KONG_PROXY_URL),
            None,
            None,
        ).await;

        if health_result.is_err() || !health_result.unwrap().1 {
            workflow_success = false;
        }

        // Step 2: Authentication (if implemented)
        if workflow_success {
            let auth_payload = json!({
                "username": format!("test_user_{}", i),
                "password": "test_password"
            });

            let auth_result = make_authenticated_request(
                &client,
                "POST",
                &format!("{}/auth/login", KONG_PROXY_URL),
                Some(&auth_payload),
                None,
            ).await;

            // Don't fail workflow if auth is not implemented
            if auth_result.is_err() {
                // Continue with workflow
            }
        }

        // Step 3: File operation
        if workflow_success {
            let file_result = make_authenticated_request(
                &client,
                "GET",
                &format!("{}/files/health", KONG_PROXY_URL),
                None,
                None,
            ).await;

            if file_result.is_err() || !file_result.unwrap().1 {
                workflow_success = false;
            }
        }

        // Step 4: Job operation
        if workflow_success {
            let job_result = make_authenticated_request(
                &client,
                "GET",
                &format!("{}/jobs/health", KONG_PROXY_URL),
                None,
                None,
            ).await;

            if job_result.is_err() || !job_result.unwrap().1 {
                workflow_success = false;
            }
        }

        let workflow_duration = workflow_start.elapsed();
        workflow_times.push(workflow_duration);

        if workflow_success {
            successful_workflows += 1;
        }

        // Small delay between workflows
        sleep(Duration::from_millis(100)).await;
    }

    let total_test_duration = workflow_times.iter().sum::<Duration>();

    let mut metrics = PerformanceMetrics::new();
    metrics.successful_requests = successful_workflows;
    metrics.failed_requests = workflow_count - successful_workflows;
    metrics.calculate_from_response_times(&workflow_times, total_test_duration);

    metrics.print_summary("End-to-End Workflow Performance");

    // Workflow performance assertions
    assert!(successful_workflows >= workflow_count * 8 / 10, "At least 80% of workflows should succeed");
    assert!(metrics.p95_response_time < Duration::from_secs(10), "95th percentile workflow time should be under 10s");
}

#[tokio::test]
async fn test_sustained_load_performance() {
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .build()
        .unwrap();

    let test_duration = Duration::from_secs(120); // 2 minutes sustained load
    let target_rps = 50.0; // Target 50 requests per second
    let request_interval = Duration::from_millis((1000.0 / target_rps) as u64);

    let mut response_times = Vec::new();
    let mut successful_requests = 0;
    let mut failed_requests = 0;

    let test_start = Instant::now();
    let end_time = test_start + test_duration;

    while Instant::now() < end_time {
        let request_start = Instant::now();
        
        let result = make_authenticated_request(
            &client,
            "GET",
            &format!("{}/auth/health", KONG_PROXY_URL),
            None,
            None,
        ).await;

        match result {
            Ok((duration, success)) => {
                response_times.push(duration);
                if success {
                    successful_requests += 1;
                } else {
                    failed_requests += 1;
                }
            }
            Err(_) => {
                response_times.push(Duration::from_millis(30000));
                failed_requests += 1;
            }
        }

        // Maintain target request rate
        let elapsed = request_start.elapsed();
        if elapsed < request_interval {
            sleep(request_interval - elapsed).await;
        }
    }

    let total_duration = test_start.elapsed();

    let mut metrics = PerformanceMetrics::new();
    metrics.successful_requests = successful_requests;
    metrics.failed_requests = failed_requests;
    metrics.calculate_from_response_times(&response_times, total_duration);

    metrics.print_summary("Sustained Load Test");

    // Sustained load assertions
    assert!(metrics.requests_per_second >= target_rps * 0.9, "Should maintain at least 90% of target RPS");
    assert!(metrics.error_rate < 0.02, "Error rate should be less than 2% under sustained load");
    assert!(metrics.p99_response_time < Duration::from_millis(5000), "99th percentile should be under 5s");
}
