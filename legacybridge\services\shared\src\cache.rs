// Caching utilities for microservices
use crate::error::{ServiceError, ServiceResult};
use redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::time::Duration;

pub struct CacheManager {
    redis_client: redis::Client,
    default_ttl: Duration,
}

impl CacheManager {
    pub fn new(redis_url: &str, default_ttl: Duration) -> ServiceResult<Self> {
        let redis_client = redis::Client::open(redis_url)?;
        Ok(Self {
            redis_client,
            default_ttl,
        })
    }

    pub async fn get<T>(&self, key: &str) -> ServiceResult<Option<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let value: Option<String> = conn.get(key).await?;
        
        match value {
            Some(json_str) => {
                let data = serde_json::from_str(&json_str)?;
                Ok(Some(data))
            }
            None => Ok(None),
        }
    }

    pub async fn set<T>(&self, key: &str, value: &T) -> ServiceResult<()>
    where
        T: Serialize,
    {
        self.set_with_ttl(key, value, self.default_ttl).await
    }

    pub async fn set_with_ttl<T>(&self, key: &str, value: &T, ttl: Duration) -> ServiceResult<()>
    where
        T: Serialize,
    {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let json_str = serde_json::to_string(value)?;
        
        conn.setex(key, ttl.as_secs() as usize, json_str).await?;
        
        Ok(())
    }

    pub async fn delete(&self, key: &str) -> ServiceResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        conn.del(key).await?;
        Ok(())
    }

    pub async fn exists(&self, key: &str) -> ServiceResult<bool> {
        let mut conn = self.redis_client.get_async_connection().await?;
        let exists: bool = conn.exists(key).await?;
        Ok(exists)
    }

    pub async fn expire(&self, key: &str, ttl: Duration) -> ServiceResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        conn.expire(key, ttl.as_secs() as usize).await?;
        Ok(())
    }

    pub async fn increment(&self, key: &str) -> ServiceResult<i64> {
        let mut conn = self.redis_client.get_async_connection().await?;
        let value: i64 = conn.incr(key, 1).await?;
        Ok(value)
    }

    pub async fn increment_with_ttl(&self, key: &str, ttl: Duration) -> ServiceResult<i64> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let value: i64 = conn.incr(key, 1).await?;
        
        // Set TTL only if this is the first increment (value == 1)
        if value == 1 {
            conn.expire(key, ttl.as_secs() as usize).await?;
        }
        
        Ok(value)
    }

    pub async fn get_keys(&self, pattern: &str) -> ServiceResult<Vec<String>> {
        let mut conn = self.redis_client.get_async_connection().await?;
        let keys: Vec<String> = conn.keys(pattern).await?;
        Ok(keys)
    }

    pub async fn flush_all(&self) -> ServiceResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        redis::cmd("FLUSHALL").query_async(&mut conn).await?;
        Ok(())
    }

    pub async fn health_check(&self) -> ServiceResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        let _: String = conn.ping().await?;
        Ok(())
    }
}

// Session management using Redis
pub struct SessionManager {
    cache: CacheManager,
    session_ttl: Duration,
}

impl SessionManager {
    pub fn new(redis_url: &str, session_ttl: Duration) -> ServiceResult<Self> {
        let cache = CacheManager::new(redis_url, session_ttl)?;
        Ok(Self {
            cache,
            session_ttl,
        })
    }

    pub async fn create_session<T>(&self, session_id: &str, data: &T) -> ServiceResult<()>
    where
        T: Serialize,
    {
        let key = format!("session:{}", session_id);
        self.cache.set_with_ttl(&key, data, self.session_ttl).await
    }

    pub async fn get_session<T>(&self, session_id: &str) -> ServiceResult<Option<T>>
    where
        T: for<'de> Deserialize<'de>,
    {
        let key = format!("session:{}", session_id);
        let session = self.cache.get(&key).await?;
        
        // Refresh TTL if session exists
        if session.is_some() {
            self.cache.expire(&key, self.session_ttl).await?;
        }
        
        Ok(session)
    }

    pub async fn update_session<T>(&self, session_id: &str, data: &T) -> ServiceResult<()>
    where
        T: Serialize,
    {
        let key = format!("session:{}", session_id);
        self.cache.set_with_ttl(&key, data, self.session_ttl).await
    }

    pub async fn delete_session(&self, session_id: &str) -> ServiceResult<()> {
        let key = format!("session:{}", session_id);
        self.cache.delete(&key).await
    }

    pub async fn session_exists(&self, session_id: &str) -> ServiceResult<bool> {
        let key = format!("session:{}", session_id);
        self.cache.exists(&key).await
    }
}

// Rate limiting using Redis
pub struct RateLimiter {
    cache: CacheManager,
}

impl RateLimiter {
    pub fn new(redis_url: &str) -> ServiceResult<Self> {
        let cache = CacheManager::new(redis_url, Duration::from_secs(3600))?;
        Ok(Self { cache })
    }

    pub async fn check_rate_limit(
        &self,
        key: &str,
        limit: u32,
        window: Duration,
    ) -> ServiceResult<RateLimitResult> {
        let count = self.cache.increment_with_ttl(key, window).await?;
        
        let remaining = if count > limit as i64 {
            0
        } else {
            limit as i64 - count
        };
        
        Ok(RateLimitResult {
            allowed: count <= limit as i64,
            count: count as u32,
            limit,
            remaining: remaining as u32,
            reset_time: chrono::Utc::now() + chrono::Duration::from_std(window).unwrap(),
        })
    }

    pub async fn reset_rate_limit(&self, key: &str) -> ServiceResult<()> {
        self.cache.delete(key).await
    }

    /// Get remaining requests for a rate limit window
    pub async fn get_remaining_requests(
        &self,
        identifier: &str,
        limit: u32,
        window: Duration,
    ) -> ServiceResult<u32> {
        let window_start = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() / window.as_secs() * window.as_secs();

        let key = format!("rate_limit:{}:{}", identifier, window_start);
        let result = self.check_rate_limit(&key, limit, window).await?;
        Ok(result.remaining)
    }

    /// Check rate limit with detailed result for horizontal scaling
    pub async fn check_rate_limit_detailed(
        &self,
        identifier: &str,
        limit: u32,
        window: Duration,
    ) -> ServiceResult<RateLimitResult> {
        let window_start = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() / window.as_secs() * window.as_secs();

        let key = format!("rate_limit:{}:{}", identifier, window_start);
        self.check_rate_limit(&key, limit, window).await
    }

    /// Batch check rate limits for multiple identifiers (for load balancing)
    pub async fn batch_check_rate_limits(
        &self,
        identifiers: Vec<&str>,
        limit: u32,
        window: Duration,
    ) -> ServiceResult<Vec<(String, RateLimitResult)>> {
        let mut results = Vec::new();

        for identifier in identifiers {
            let result = self.check_rate_limit_detailed(identifier, limit, window).await?;
            results.push((identifier.to_string(), result));
        }

        Ok(results)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitResult {
    pub allowed: bool,
    pub count: u32,
    pub limit: u32,
    pub remaining: u32,
    pub reset_time: chrono::DateTime<chrono::Utc>,
}

// Cache warming utilities
pub struct CacheWarmer {
    cache: CacheManager,
}

impl CacheWarmer {
    pub fn new(redis_url: &str) -> ServiceResult<Self> {
        let cache = CacheManager::new(redis_url, Duration::from_secs(3600))?;
        Ok(Self { cache })
    }

    pub async fn warm_cache<T, F, Fut>(&self, key: &str, loader: F, ttl: Duration) -> ServiceResult<T>
    where
        T: Serialize + for<'de> Deserialize<'de> + Clone,
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = ServiceResult<T>>,
    {
        // Try to get from cache first
        if let Some(cached_value) = self.cache.get::<T>(key).await? {
            return Ok(cached_value);
        }

        // Load data and cache it
        let data = loader().await?;
        self.cache.set_with_ttl(key, &data, ttl).await?;
        
        Ok(data)
    }

    pub async fn invalidate_pattern(&self, pattern: &str) -> ServiceResult<u32> {
        let keys = self.cache.get_keys(pattern).await?;
        let mut count = 0;
        
        for key in keys {
            self.cache.delete(&key).await?;
            count += 1;
        }
        
        Ok(count)
    }
}

// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub hits: u64,
    pub misses: u64,
    pub hit_rate: f64,
    pub total_keys: u64,
    pub memory_usage: u64,
}

impl CacheManager {
    pub async fn get_stats(&self) -> ServiceResult<CacheStats> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // Get Redis INFO stats
        let info: String = redis::cmd("INFO").arg("stats").query_async(&mut conn).await?;
        
        // Parse the info string (simplified parsing)
        let mut hits = 0u64;
        let mut misses = 0u64;
        let mut total_keys = 0u64;
        let mut memory_usage = 0u64;
        
        for line in info.lines() {
            if line.starts_with("keyspace_hits:") {
                hits = line.split(':').nth(1).unwrap_or("0").parse().unwrap_or(0);
            } else if line.starts_with("keyspace_misses:") {
                misses = line.split(':').nth(1).unwrap_or("0").parse().unwrap_or(0);
            }
        }
        
        // Get total keys
        let dbsize: u64 = redis::cmd("DBSIZE").query_async(&mut conn).await?;
        total_keys = dbsize;
        
        // Get memory usage
        let memory_info: String = redis::cmd("INFO").arg("memory").query_async(&mut conn).await?;
        for line in memory_info.lines() {
            if line.starts_with("used_memory:") {
                memory_usage = line.split(':').nth(1).unwrap_or("0").parse().unwrap_or(0);
                break;
            }
        }
        
        let hit_rate = if hits + misses > 0 {
            hits as f64 / (hits + misses) as f64
        } else {
            0.0
        };
        
        Ok(CacheStats {
            hits,
            misses,
            hit_rate,
            total_keys,
            memory_usage,
        })
    }
}
