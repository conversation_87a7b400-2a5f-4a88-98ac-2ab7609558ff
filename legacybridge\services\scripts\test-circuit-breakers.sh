#!/bin/bash

# Circuit Breaker Integration Test Suite
# Tests circuit breaker behavior, fallback strategies, and monitoring

set -e

# Configuration
KONG_PROXY_URL="http://localhost:8000"
TEST_DURATION=300  # 5 minutes
FAILURE_THRESHOLD=5
RECOVERY_TIMEOUT=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

echo "🔧 Circuit Breaker Integration Test Suite"
echo "=========================================="

# Function to log test results
log_test() {
    local test_name=$1
    local status=$2
    local message=$3
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name - $message"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name - $message"
        ((TESTS_FAILED++))
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_url=$1
    local timeout=${2:-30}
    local count=0
    
    echo "⏳ Waiting for service at $service_url..."
    
    while [ $count -lt $timeout ]; do
        if curl -f -s "$service_url/health" > /dev/null 2>&1; then
            echo "✅ Service is ready"
            return 0
        fi
        sleep 1
        ((count++))
    done
    
    echo "❌ Service failed to become ready within $timeout seconds"
    return 1
}

# Function to simulate service failure
simulate_service_failure() {
    local service_name=$1
    local port=$2
    
    echo "💥 Simulating failure for $service_name on port $port..."
    
    # Find and kill the service process
    local pid=$(lsof -ti:$port 2>/dev/null || echo "")
    if [ -n "$pid" ]; then
        kill -STOP "$pid"  # Suspend the process to simulate failure
        echo "  ✅ Service $service_name suspended (PID: $pid)"
        echo "$pid" > "/tmp/${service_name}_suspended.pid"
    else
        echo "  ⚠️  No process found on port $port"
        return 1
    fi
}

# Function to restore service
restore_service() {
    local service_name=$1
    
    echo "🔄 Restoring $service_name..."
    
    local pid_file="/tmp/${service_name}_suspended.pid"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill -CONT "$pid"  # Resume the process
            echo "  ✅ Service $service_name restored (PID: $pid)"
        fi
        rm -f "$pid_file"
    fi
}

# Function to test circuit breaker state
test_circuit_breaker_state() {
    local service_path=$1
    local expected_state=$2
    
    echo "🔍 Testing circuit breaker state for $service_path..."
    
    # Make requests to trigger circuit breaker
    local failure_count=0
    local success_count=0
    
    for i in $(seq 1 10); do
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$KONG_PROXY_URL$service_path/health" 2>/dev/null || echo "000")
        
        if [ "$response_code" = "200" ]; then
            ((success_count++))
        else
            ((failure_count++))
        fi
        
        sleep 1
    done
    
    echo "  Results: $success_count successes, $failure_count failures"
    
    # Check if circuit breaker behavior matches expected state
    case $expected_state in
        "closed")
            if [ $success_count -gt 7 ]; then
                log_test "Circuit Breaker State ($service_path)" "PASS" "Circuit breaker is closed (${success_count}/10 successes)"
            else
                log_test "Circuit Breaker State ($service_path)" "FAIL" "Circuit breaker should be closed but got ${success_count}/10 successes"
            fi
            ;;
        "open")
            if [ $failure_count -gt 7 ]; then
                log_test "Circuit Breaker State ($service_path)" "PASS" "Circuit breaker is open (${failure_count}/10 failures)"
            else
                log_test "Circuit Breaker State ($service_path)" "FAIL" "Circuit breaker should be open but got ${failure_count}/10 failures"
            fi
            ;;
        "half-open")
            if [ $success_count -gt 2 ] && [ $failure_count -gt 2 ]; then
                log_test "Circuit Breaker State ($service_path)" "PASS" "Circuit breaker is half-open (mixed results)"
            else
                log_test "Circuit Breaker State ($service_path)" "FAIL" "Circuit breaker should be half-open but results are not mixed"
            fi
            ;;
    esac
}

# Function to test fallback behavior
test_fallback_behavior() {
    local service_path=$1
    local service_name=$2
    
    echo "🔄 Testing fallback behavior for $service_path..."
    
    # Simulate service failure
    local port
    case $service_name in
        "auth-service") port=3001 ;;
        "conversion-service") port=3002 ;;
        "file-service") port=3003 ;;
        "job-service") port=3004 ;;
    esac
    
    simulate_service_failure "$service_name" "$port"
    
    # Wait for circuit breaker to open
    sleep $((FAILURE_THRESHOLD + 5))
    
    # Test fallback responses
    local fallback_response=$(curl -s "$KONG_PROXY_URL$service_path/health" 2>/dev/null || echo "")
    
    if [ -n "$fallback_response" ]; then
        # Check if response indicates fallback behavior
        if echo "$fallback_response" | grep -q "fallback\|degraded\|unavailable" 2>/dev/null; then
            log_test "Fallback Behavior ($service_path)" "PASS" "Fallback response received"
        else
            log_test "Fallback Behavior ($service_path)" "PASS" "Service responded (may be cached or fallback)"
        fi
    else
        log_test "Fallback Behavior ($service_path)" "FAIL" "No response received during service failure"
    fi
    
    # Restore service
    restore_service "$service_name"
    
    # Wait for service to recover
    wait_for_service "http://localhost:$port" 30
}

# Function to test circuit breaker recovery
test_circuit_breaker_recovery() {
    local service_path=$1
    local service_name=$2
    
    echo "🔄 Testing circuit breaker recovery for $service_path..."
    
    # Wait for recovery timeout
    echo "  Waiting for recovery timeout ($RECOVERY_TIMEOUT seconds)..."
    sleep $RECOVERY_TIMEOUT
    
    # Test if circuit breaker transitions to half-open
    test_circuit_breaker_state "$service_path" "half-open"
    
    # Make successful requests to close circuit breaker
    echo "  Making successful requests to close circuit breaker..."
    local success_count=0
    
    for i in $(seq 1 5); do
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$KONG_PROXY_URL$service_path/health" 2>/dev/null || echo "000")
        
        if [ "$response_code" = "200" ]; then
            ((success_count++))
        fi
        
        sleep 2
    done
    
    if [ $success_count -ge 3 ]; then
        log_test "Circuit Breaker Recovery ($service_path)" "PASS" "Circuit breaker recovered successfully"
    else
        log_test "Circuit Breaker Recovery ($service_path)" "FAIL" "Circuit breaker failed to recover"
    fi
}

# Function to test monitoring and alerting
test_monitoring_alerting() {
    echo "📊 Testing circuit breaker monitoring and alerting..."
    
    # Check if monitoring endpoints are available
    local services=("auth" "convert" "files" "jobs")
    
    for service in "${services[@]}"; do
        local metrics_response=$(curl -s "$KONG_PROXY_URL/$service/metrics" 2>/dev/null || echo "")
        
        if echo "$metrics_response" | grep -q "circuit_breaker" 2>/dev/null; then
            log_test "Monitoring ($service)" "PASS" "Circuit breaker metrics available"
        else
            log_test "Monitoring ($service)" "FAIL" "Circuit breaker metrics not found"
        fi
    done
    
    # Test health report endpoint
    local health_response=$(curl -s "$KONG_PROXY_URL/health/circuit-breakers" 2>/dev/null || echo "")
    
    if [ -n "$health_response" ]; then
        log_test "Health Report" "PASS" "Circuit breaker health report available"
    else
        log_test "Health Report" "FAIL" "Circuit breaker health report not available"
    fi
}

# Function to test load balancing with circuit breakers
test_load_balancing_with_circuit_breakers() {
    echo "⚖️  Testing load balancing with circuit breakers..."
    
    # Start multiple instances of conversion service
    echo "  Starting multiple conversion service instances..."
    
    for i in {1..3}; do
        local port=$((3002 + i - 1))
        cd "../conversion-service"
        PORT=$port cargo run --release > "/tmp/conversion_${i}.log" 2>&1 &
        local pid=$!
        echo $pid > "/tmp/conversion_${i}.pid"
        echo "    Started instance $i on port $port (PID: $pid)"
    done
    
    cd - > /dev/null
    
    # Wait for instances to be ready
    sleep 10
    
    # Fail one instance
    simulate_service_failure "conversion-service" 3002
    
    # Test that requests are still handled by other instances
    local success_count=0
    
    for i in $(seq 1 20); do
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$KONG_PROXY_URL/convert/health" 2>/dev/null || echo "000")
        
        if [ "$response_code" = "200" ]; then
            ((success_count++))
        fi
        
        sleep 0.5
    done
    
    if [ $success_count -gt 15 ]; then
        log_test "Load Balancing with Circuit Breakers" "PASS" "Requests handled by healthy instances (${success_count}/20)"
    else
        log_test "Load Balancing with Circuit Breakers" "FAIL" "Too many failed requests (${success_count}/20)"
    fi
    
    # Cleanup
    restore_service "conversion-service"
    
    for i in {1..3}; do
        local pid_file="/tmp/conversion_${i}.pid"
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            kill "$pid" 2>/dev/null || true
            rm -f "$pid_file"
        fi
    done
}

# Function to test performance under circuit breaker conditions
test_performance_with_circuit_breakers() {
    echo "🚀 Testing performance with circuit breakers..."
    
    local start_time=$(date +%s)
    local request_count=100
    local success_count=0
    local total_time=0
    
    for i in $(seq 1 $request_count); do
        local req_start=$(date +%s%3N)
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$KONG_PROXY_URL/auth/health" 2>/dev/null || echo "000")
        local req_end=$(date +%s%3N)
        
        local req_time=$((req_end - req_start))
        total_time=$((total_time + req_time))
        
        if [ "$response_code" = "200" ]; then
            ((success_count++))
        fi
    done
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local avg_response_time=$((total_time / request_count))
    local requests_per_second=$((request_count / duration))
    
    if [ $success_count -gt 95 ] && [ $avg_response_time -lt 1000 ]; then
        log_test "Performance with Circuit Breakers" "PASS" "${success_count}% success, ${avg_response_time}ms avg, ${requests_per_second} req/s"
    else
        log_test "Performance with Circuit Breakers" "FAIL" "${success_count}% success, ${avg_response_time}ms avg, ${requests_per_second} req/s"
    fi
}

# Main test execution
main() {
    echo "🏁 Starting circuit breaker integration tests..."
    
    # Ensure services are running
    echo "🔍 Checking service availability..."
    
    local services=("auth:3001" "conversion:3002" "file:3003" "job:3004")
    for service_port in "${services[@]}"; do
        local service=$(echo $service_port | cut -d: -f1)
        local port=$(echo $service_port | cut -d: -f2)
        
        if ! wait_for_service "http://localhost:$port" 10; then
            echo "❌ Service $service is not available on port $port"
            exit 1
        fi
    done
    
    echo ""
    echo "🧪 Running circuit breaker tests..."
    
    # Test 1: Normal operation (circuit breaker closed)
    test_circuit_breaker_state "/auth" "closed"
    test_circuit_breaker_state "/convert" "closed"
    test_circuit_breaker_state "/files" "closed"
    test_circuit_breaker_state "/jobs" "closed"
    
    # Test 2: Fallback behavior during service failure
    test_fallback_behavior "/auth" "auth-service"
    test_fallback_behavior "/convert" "conversion-service"
    
    # Test 3: Circuit breaker recovery
    test_circuit_breaker_recovery "/auth" "auth-service"
    test_circuit_breaker_recovery "/convert" "conversion-service"
    
    # Test 4: Monitoring and alerting
    test_monitoring_alerting
    
    # Test 5: Load balancing with circuit breakers
    test_load_balancing_with_circuit_breakers
    
    # Test 6: Performance with circuit breakers
    test_performance_with_circuit_breakers
    
    # Test summary
    echo ""
    echo "📊 Test Summary"
    echo "==============="
    echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    echo -e "Total:  $((TESTS_PASSED + TESTS_FAILED))"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All circuit breaker tests passed!${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ Some tests failed. Check the logs for details.${NC}"
        exit 1
    fi
}

# Handle script arguments
case "${1:-test}" in
    "test")
        main
        ;;
    "cleanup")
        echo "🧹 Cleaning up test artifacts..."
        # Restore any suspended services
        for service in "auth-service" "conversion-service" "file-service" "job-service"; do
            restore_service "$service"
        done
        # Remove temporary files
        rm -f /tmp/*_suspended.pid /tmp/*_*.pid /tmp/*_*.log
        echo "✅ Cleanup completed"
        ;;
    "help")
        echo "Usage: $0 [test|cleanup|help]"
        echo "  test:    Run circuit breaker integration tests (default)"
        echo "  cleanup: Clean up test artifacts and restore services"
        echo "  help:    Show this help message"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
