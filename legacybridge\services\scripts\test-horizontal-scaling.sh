#!/bin/bash

# Horizontal Scaling Test Suite for LegacyBridge Microservices
# Tests multi-instance deployment, load balancing, and failover scenarios

set -e

# Configuration
KONG_ADMIN_URL="http://localhost:8001"
KONG_PROXY_URL="http://localhost:8000"
TEST_DURATION=60
CONCURRENT_REQUESTS=10
TOTAL_REQUESTS=1000

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

echo "🧪 LegacyBridge Horizontal Scaling Test Suite"
echo "=============================================="

# Function to log test results
log_test() {
    local test_name=$1
    local status=$2
    local message=$3
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name - $message"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name - $message"
        ((TESTS_FAILED++))
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_url=$1
    local timeout=${2:-30}
    local count=0
    
    echo "⏳ Waiting for service at $service_url..."
    
    while [ $count -lt $timeout ]; do
        if curl -f -s "$service_url/health" > /dev/null 2>&1; then
            echo "✅ Service is ready"
            return 0
        fi
        sleep 1
        ((count++))
    done
    
    echo "❌ Service failed to become ready within $timeout seconds"
    return 1
}

# Function to start multiple service instances
start_service_instances() {
    local service_name=$1
    local base_port=$2
    local instances=${3:-3}
    
    echo "🚀 Starting $instances instances of $service_name..."
    
    for i in $(seq 1 $instances); do
        local port=$((base_port + i - 1))
        echo "  Starting $service_name instance $i on port $port..."
        
        # Start service instance in background
        cd "../$service_name"
        PORT=$port cargo run --release > "/tmp/${service_name}_${i}.log" 2>&1 &
        local pid=$!
        echo $pid > "/tmp/${service_name}_${i}.pid"
        
        # Wait for instance to be ready
        if wait_for_service "http://localhost:$port" 10; then
            echo "  ✅ Instance $i started successfully (PID: $pid)"
        else
            echo "  ❌ Instance $i failed to start"
            return 1
        fi
    done
    
    cd - > /dev/null
    return 0
}

# Function to stop service instances
stop_service_instances() {
    local service_name=$1
    local instances=${2:-3}
    
    echo "🛑 Stopping $service_name instances..."
    
    for i in $(seq 1 $instances); do
        local pid_file="/tmp/${service_name}_${i}.pid"
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                echo "  ✅ Stopped instance $i (PID: $pid)"
            fi
            rm -f "$pid_file"
        fi
    done
}

# Function to test load balancing
test_load_balancing() {
    local service_path=$1
    local expected_instances=$2
    
    echo "⚖️  Testing load balancing for $service_path..."
    
    # Make multiple requests and track which instances respond
    declare -A instance_counts
    local total_requests=20
    
    for i in $(seq 1 $total_requests); do
        local response=$(curl -s "$KONG_PROXY_URL$service_path/health" | jq -r '.instance_id // "unknown"' 2>/dev/null || echo "unknown")
        if [ "$response" != "unknown" ]; then
            instance_counts["$response"]=$((${instance_counts["$response"]:-0} + 1))
        fi
    done
    
    # Check if requests were distributed across instances
    local unique_instances=${#instance_counts[@]}
    
    if [ $unique_instances -ge $expected_instances ]; then
        log_test "Load Balancing ($service_path)" "PASS" "Requests distributed across $unique_instances instances"
        
        # Show distribution
        for instance in "${!instance_counts[@]}"; do
            echo "    Instance $instance: ${instance_counts[$instance]} requests"
        done
    else
        log_test "Load Balancing ($service_path)" "FAIL" "Only $unique_instances instances received requests (expected $expected_instances)"
    fi
}

# Function to test failover
test_failover() {
    local service_path=$1
    local service_name=$2
    
    echo "🔄 Testing failover for $service_path..."
    
    # Get initial upstream health
    local initial_healthy=$(curl -s "$KONG_ADMIN_URL/upstreams/${service_name}-upstream/health" | jq '.data | length')
    
    # Stop one instance
    echo "  Stopping one instance..."
    local pid_file="/tmp/${service_name}_1.pid"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        kill "$pid" 2>/dev/null || true
    fi
    
    # Wait for Kong to detect the failure
    sleep 35  # Health check interval is 30 seconds
    
    # Check if service is still accessible
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$KONG_PROXY_URL$service_path/health")
    
    if [ "$response_code" = "200" ]; then
        log_test "Failover ($service_path)" "PASS" "Service remained accessible after instance failure"
    else
        log_test "Failover ($service_path)" "FAIL" "Service became inaccessible (HTTP $response_code)"
    fi
    
    # Restart the instance
    echo "  Restarting failed instance..."
    local base_port
    case $service_name in
        "auth-service") base_port=3001 ;;
        "conversion-service") base_port=3002 ;;
        "file-service") base_port=3003 ;;
        "job-service") base_port=3004 ;;
    esac
    
    cd "../$service_name"
    PORT=$base_port cargo run --release > "/tmp/${service_name}_1.log" 2>&1 &
    local new_pid=$!
    echo $new_pid > "/tmp/${service_name}_1.pid"
    cd - > /dev/null
    
    wait_for_service "http://localhost:$base_port" 10
}

# Function to test session persistence
test_session_persistence() {
    echo "🔐 Testing session persistence across instances..."
    
    # Login and get session token
    local login_response=$(curl -s -X POST "$KONG_PROXY_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username":"test","password":"test"}')
    
    local token=$(echo "$login_response" | jq -r '.token // empty' 2>/dev/null)
    
    if [ -n "$token" ]; then
        # Make authenticated requests to different services
        local auth_check=$(curl -s -H "Authorization: Bearer $token" "$KONG_PROXY_URL/auth/validate" | jq -r '.valid // false')
        
        if [ "$auth_check" = "true" ]; then
            log_test "Session Persistence" "PASS" "Session token valid across service instances"
        else
            log_test "Session Persistence" "FAIL" "Session token validation failed"
        fi
    else
        log_test "Session Persistence" "FAIL" "Failed to obtain session token"
    fi
}

# Function to run performance test
test_performance_under_load() {
    local service_path=$1
    
    echo "🚀 Testing performance under load for $service_path..."
    
    # Use Apache Bench if available, otherwise use curl loop
    if command -v ab > /dev/null; then
        local ab_output=$(ab -n $TOTAL_REQUESTS -c $CONCURRENT_REQUESTS "$KONG_PROXY_URL$service_path/health" 2>/dev/null)
        local requests_per_second=$(echo "$ab_output" | grep "Requests per second" | awk '{print $4}')
        local failed_requests=$(echo "$ab_output" | grep "Failed requests" | awk '{print $3}')
        
        if [ "${failed_requests:-0}" -eq 0 ] && [ "$(echo "$requests_per_second > 100" | bc -l 2>/dev/null || echo 0)" -eq 1 ]; then
            log_test "Performance ($service_path)" "PASS" "$requests_per_second req/sec, 0 failures"
        else
            log_test "Performance ($service_path)" "FAIL" "$requests_per_second req/sec, $failed_requests failures"
        fi
    else
        echo "  ⚠️  Apache Bench not available, using simple curl test..."
        local start_time=$(date +%s)
        local success_count=0
        
        for i in $(seq 1 100); do
            if curl -f -s "$KONG_PROXY_URL$service_path/health" > /dev/null 2>&1; then
                ((success_count++))
            fi
        done
        
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        local req_per_sec=$((success_count / duration))
        
        if [ $success_count -eq 100 ] && [ $req_per_sec -gt 10 ]; then
            log_test "Performance ($service_path)" "PASS" "$req_per_sec req/sec, $success_count/100 successful"
        else
            log_test "Performance ($service_path)" "FAIL" "$req_per_sec req/sec, $success_count/100 successful"
        fi
    fi
}

# Main test execution
main() {
    echo "🏁 Starting horizontal scaling tests..."
    
    # Ensure Kong is running
    if ! curl -f -s "$KONG_ADMIN_URL/status" > /dev/null 2>&1; then
        echo "❌ Kong Admin API is not accessible at $KONG_ADMIN_URL"
        exit 1
    fi
    
    # Start multiple instances of each service
    echo "🚀 Starting service instances..."
    start_service_instances "auth-service" 3001 3
    start_service_instances "conversion-service" 3002 3
    start_service_instances "file-service" 3003 3
    start_service_instances "job-service" 3004 3
    
    # Configure Kong with multiple targets
    echo "⚙️  Configuring Kong load balancing..."
    ./setup-kong-advanced-routing.sh
    
    # Wait for Kong configuration to propagate
    sleep 10
    
    # Run tests
    echo "🧪 Running horizontal scaling tests..."
    
    test_load_balancing "/auth" 3
    test_load_balancing "/convert" 3
    test_load_balancing "/files" 3
    test_load_balancing "/jobs" 3
    
    test_failover "/auth" "auth-service"
    test_failover "/convert" "conversion-service"
    
    test_session_persistence
    
    test_performance_under_load "/auth"
    test_performance_under_load "/convert"
    
    # Cleanup
    echo "🧹 Cleaning up..."
    stop_service_instances "auth-service" 3
    stop_service_instances "conversion-service" 3
    stop_service_instances "file-service" 3
    stop_service_instances "job-service" 3
    
    # Test summary
    echo ""
    echo "📊 Test Summary"
    echo "==============="
    echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    echo -e "Total:  $((TESTS_PASSED + TESTS_FAILED))"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All horizontal scaling tests passed!${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ Some tests failed. Check the logs for details.${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
