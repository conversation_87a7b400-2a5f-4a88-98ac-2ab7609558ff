#!/bin/bash

# Deploy Comprehensive Monitoring Stack for LegacyBridge
# Deploys Prometheus, Grafana, AlertManager, and custom dashboards

set -e

# Configuration
MONITORING_NAMESPACE="monitoring"
LEGACYBRIDGE_NAMESPACE="legacybridge"
KUBECTL_TIMEOUT="300s"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "📊 Deploying Comprehensive Monitoring Stack for LegacyBridge"
echo "==========================================================="

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}❌ kubectl is not installed or not in PATH${NC}"
        exit 1
    fi
    
    echo -e "${G<PERSON><PERSON>}✅ kubectl is available${NC}"
}

# Function to check if namespace exists
check_namespace() {
    local namespace=$1
    
    if kubectl get namespace "$namespace" &> /dev/null; then
        echo -e "${GREEN}✅ Namespace $namespace exists${NC}"
    else
        echo -e "${YELLOW}⚠️  Creating namespace $namespace${NC}"
        kubectl create namespace "$namespace"
    fi
}

# Function to apply Kubernetes manifests
apply_manifest() {
    local manifest_file=$1
    local description=$2
    
    echo -e "${BLUE}📄 Applying $description...${NC}"
    
    if kubectl apply -f "$manifest_file" --timeout="$KUBECTL_TIMEOUT"; then
        echo -e "${GREEN}✅ Successfully applied $description${NC}"
    else
        echo -e "${RED}❌ Failed to apply $description${NC}"
        return 1
    fi
}

# Function to wait for deployment to be ready
wait_for_deployment() {
    local deployment_name=$1
    local namespace=$2
    local timeout=${3:-300}
    
    echo -e "${BLUE}⏳ Waiting for deployment $deployment_name to be ready...${NC}"
    
    if kubectl wait --for=condition=available deployment/"$deployment_name" \
        --namespace="$namespace" --timeout="${timeout}s"; then
        echo -e "${GREEN}✅ Deployment $deployment_name is ready${NC}"
    else
        echo -e "${RED}❌ Deployment $deployment_name failed to become ready${NC}"
        return 1
    fi
}

# Function to check service accessibility
check_service() {
    local service_name=$1
    local namespace=$2
    local port=$3
    
    echo -e "${BLUE}🔍 Checking service $service_name accessibility...${NC}"
    
    # Port forward to test service
    kubectl port-forward -n "$namespace" "svc/$service_name" "$port:$port" &
    local port_forward_pid=$!
    
    # Wait a moment for port forward to establish
    sleep 5
    
    # Test service accessibility
    if curl -f -s "http://localhost:$port" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Service $service_name is accessible on port $port${NC}"
        local result=0
    else
        echo -e "${YELLOW}⚠️  Service $service_name may not be fully ready yet${NC}"
        local result=1
    fi
    
    # Kill port forward
    kill $port_forward_pid 2>/dev/null || true
    
    return $result
}

# Function to setup Grafana dashboards
setup_grafana_dashboards() {
    echo -e "${BLUE}📊 Setting up Grafana dashboards...${NC}"
    
    # Wait for Grafana to be ready
    wait_for_deployment "grafana" "$MONITORING_NAMESPACE" 300
    
    # Port forward to Grafana
    kubectl port-forward -n "$MONITORING_NAMESPACE" svc/grafana 3000:3000 &
    local grafana_pid=$!
    
    # Wait for Grafana to be accessible
    local retries=0
    while [ $retries -lt 30 ]; do
        if curl -f -s "http://localhost:3000" > /dev/null 2>&1; then
            break
        fi
        sleep 2
        ((retries++))
    done
    
    if [ $retries -eq 30 ]; then
        echo -e "${RED}❌ Grafana is not accessible after 60 seconds${NC}"
        kill $grafana_pid 2>/dev/null || true
        return 1
    fi
    
    echo -e "${GREEN}✅ Grafana is accessible${NC}"
    echo -e "${BLUE}📊 Grafana is available at: http://localhost:3000${NC}"
    echo -e "${BLUE}📊 Default credentials: admin/admin123${NC}"
    
    # Kill port forward
    kill $grafana_pid 2>/dev/null || true
}

# Function to verify monitoring stack
verify_monitoring_stack() {
    echo -e "${BLUE}🔍 Verifying monitoring stack...${NC}"
    
    # Check Prometheus
    echo "  Checking Prometheus..."
    if kubectl get deployment prometheus -n "$MONITORING_NAMESPACE" &> /dev/null; then
        echo -e "    ${GREEN}✅ Prometheus deployment exists${NC}"
    else
        echo -e "    ${RED}❌ Prometheus deployment not found${NC}"
        return 1
    fi
    
    # Check Grafana
    echo "  Checking Grafana..."
    if kubectl get deployment grafana -n "$MONITORING_NAMESPACE" &> /dev/null; then
        echo -e "    ${GREEN}✅ Grafana deployment exists${NC}"
    else
        echo -e "    ${RED}❌ Grafana deployment not found${NC}"
        return 1
    fi
    
    # Check AlertManager
    echo "  Checking AlertManager..."
    if kubectl get deployment alertmanager -n "$MONITORING_NAMESPACE" &> /dev/null; then
        echo -e "    ${GREEN}✅ AlertManager deployment exists${NC}"
    else
        echo -e "    ${RED}❌ AlertManager deployment not found${NC}"
        return 1
    fi
    
    # Check ServiceMonitors
    echo "  Checking ServiceMonitors..."
    local servicemonitor_count=$(kubectl get servicemonitor -n "$LEGACYBRIDGE_NAMESPACE" --no-headers 2>/dev/null | wc -l)
    if [ "$servicemonitor_count" -gt 0 ]; then
        echo -e "    ${GREEN}✅ Found $servicemonitor_count ServiceMonitors${NC}"
    else
        echo -e "    ${YELLOW}⚠️  No ServiceMonitors found${NC}"
    fi
    
    # Check PrometheusRules
    echo "  Checking PrometheusRules..."
    local prometheusrule_count=$(kubectl get prometheusrule -n "$LEGACYBRIDGE_NAMESPACE" --no-headers 2>/dev/null | wc -l)
    if [ "$prometheusrule_count" -gt 0 ]; then
        echo -e "    ${GREEN}✅ Found $prometheusrule_count PrometheusRules${NC}"
    else
        echo -e "    ${YELLOW}⚠️  No PrometheusRules found${NC}"
    fi
}

# Function to show access information
show_access_info() {
    echo ""
    echo "🌐 Monitoring Stack Access Information"
    echo "====================================="
    
    echo -e "${BLUE}📊 Prometheus:${NC}"
    echo "  kubectl port-forward -n $MONITORING_NAMESPACE svc/prometheus 9090:9090"
    echo "  Then access: http://localhost:9090"
    
    echo ""
    echo -e "${BLUE}📊 Grafana:${NC}"
    echo "  kubectl port-forward -n $MONITORING_NAMESPACE svc/grafana 3000:3000"
    echo "  Then access: http://localhost:3000"
    echo "  Default credentials: admin/admin123"
    
    echo ""
    echo -e "${BLUE}🚨 AlertManager:${NC}"
    echo "  kubectl port-forward -n $MONITORING_NAMESPACE svc/alertmanager 9093:9093"
    echo "  Then access: http://localhost:9093"
    
    echo ""
    echo -e "${BLUE}📈 Available Dashboards:${NC}"
    echo "  - LegacyBridge Overview"
    echo "  - Circuit Breakers"
    echo "  - Auto-Scaling Metrics"
    echo "  - Service Performance"
}

# Main deployment function
main() {
    echo "🔍 Pre-deployment checks..."
    
    # Check prerequisites
    check_kubectl
    check_namespace "$MONITORING_NAMESPACE"
    check_namespace "$LEGACYBRIDGE_NAMESPACE"
    
    echo ""
    echo "📦 Deploying monitoring stack components..."
    
    # Deploy Prometheus rules first
    apply_manifest "../k8s/prometheus-rules.yaml" "Prometheus alerting rules"
    
    # Deploy Grafana dashboards
    apply_manifest "../k8s/grafana-dashboards.yaml" "Grafana dashboards"
    
    # Deploy main monitoring stack
    apply_manifest "../k8s/monitoring-stack.yaml" "Monitoring stack (Prometheus, Grafana, AlertManager)"
    
    echo ""
    echo "⏳ Waiting for deployments to be ready..."
    
    # Wait for deployments
    wait_for_deployment "prometheus" "$MONITORING_NAMESPACE" 300
    wait_for_deployment "grafana" "$MONITORING_NAMESPACE" 300
    wait_for_deployment "alertmanager" "$MONITORING_NAMESPACE" 300
    
    echo ""
    echo "🔧 Setting up Grafana dashboards..."
    setup_grafana_dashboards
    
    echo ""
    echo "🔍 Verifying monitoring stack..."
    verify_monitoring_stack
    
    echo ""
    echo "📊 Monitoring stack deployment summary:"
    echo "======================================"
    
    # Show deployment status
    echo -e "${BLUE}Deployments:${NC}"
    kubectl get deployments -n "$MONITORING_NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}Services:${NC}"
    kubectl get services -n "$MONITORING_NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}ConfigMaps:${NC}"
    kubectl get configmaps -n "$MONITORING_NAMESPACE"
    
    # Show access information
    show_access_info
    
    echo ""
    echo -e "${GREEN}🎉 Monitoring stack deployment completed successfully!${NC}"
    
    echo ""
    echo "🎯 Next steps:"
    echo "=============="
    echo "1. Access Grafana and explore the dashboards"
    echo "2. Configure AlertManager with your notification channels"
    echo "3. Verify that metrics are being collected from all services"
    echo "4. Test alerting by simulating service failures"
    echo "5. Customize dashboards and alerts based on your requirements"
}

# Function to show monitoring status
show_status() {
    echo "📊 Monitoring Stack Status"
    echo "=========================="
    
    echo -e "${BLUE}Deployments:${NC}"
    kubectl get deployments -n "$MONITORING_NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}Pods:${NC}"
    kubectl get pods -n "$MONITORING_NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}Services:${NC}"
    kubectl get services -n "$MONITORING_NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}ServiceMonitors:${NC}"
    kubectl get servicemonitor -n "$LEGACYBRIDGE_NAMESPACE" -o wide 2>/dev/null || echo "No ServiceMonitors found"
    
    echo ""
    echo -e "${BLUE}PrometheusRules:${NC}"
    kubectl get prometheusrule -n "$LEGACYBRIDGE_NAMESPACE" -o wide 2>/dev/null || echo "No PrometheusRules found"
}

# Function to clean up monitoring stack
cleanup() {
    echo "🧹 Cleaning up monitoring stack..."
    
    echo "  Removing monitoring stack components..."
    kubectl delete -f "../k8s/monitoring-stack.yaml" --ignore-not-found=true
    
    echo "  Removing Grafana dashboards..."
    kubectl delete -f "../k8s/grafana-dashboards.yaml" --ignore-not-found=true
    
    echo "  Removing Prometheus rules..."
    kubectl delete -f "../k8s/prometheus-rules.yaml" --ignore-not-found=true
    
    echo "  Removing ServiceMonitors and PrometheusRules from legacybridge namespace..."
    kubectl delete servicemonitor,prometheusrule --all -n "$LEGACYBRIDGE_NAMESPACE" --ignore-not-found=true
    
    echo -e "${GREEN}✅ Monitoring stack cleanup completed${NC}"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    "access")
        show_access_info
        ;;
    "help")
        echo "Usage: $0 [deploy|status|cleanup|access|help]"
        echo "  deploy:  Deploy monitoring stack (default)"
        echo "  status:  Show monitoring stack status"
        echo "  cleanup: Remove monitoring stack"
        echo "  access:  Show access information"
        echo "  help:    Show this help message"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
