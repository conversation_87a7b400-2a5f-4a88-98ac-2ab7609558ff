// Integration tests for service discovery functionality
use shared::service_discovery::{ServiceRegistry, ServiceInstance, LoadBalancingStrategy, ServiceDiscoveryClient};
use shared::cache::CacheManager;
use std::sync::Arc;
use std::time::Duration;
use std::collections::HashMap;
use tokio::time::sleep;

async fn setup_test_registry() -> Arc<ServiceRegistry> {
    // Use a test Redis instance or mock
    let redis_url = std::env::var("TEST_REDIS_URL").unwrap_or_else(|_| "redis://localhost:6379".to_string());
    
    Arc::new(
        ServiceRegistry::new(
            &redis_url,
            Duration::from_secs(5),  // health check interval
            Duration::from_secs(2),  // health check timeout
        ).expect("Failed to create service registry")
    )
}

#[tokio::test]
async fn test_service_instance_creation() {
    let instance = ServiceInstance::new(
        "auth-service".to_string(),
        "localhost".to_string(),
        3001,
    );
    
    assert_eq!(instance.name, "auth-service");
    assert_eq!(instance.host, "localhost");
    assert_eq!(instance.port, 3001);
    assert_eq!(instance.endpoint(), "http://localhost:3001");
    assert_eq!(instance.health_check_url, "http://localhost:3001/health");
    assert!(instance.healthy);
    assert_eq!(instance.weight, 100);
    assert!(instance.metadata.is_empty());
}

#[tokio::test]
async fn test_service_instance_with_metadata() {
    let instance = ServiceInstance::new(
        "conversion-service".to_string(),
        "localhost".to_string(),
        3002,
    )
    .with_metadata("version".to_string(), "1.0.0".to_string())
    .with_metadata("region".to_string(), "us-west-2".to_string())
    .with_weight(150);
    
    assert_eq!(instance.weight, 150);
    assert_eq!(instance.metadata.get("version"), Some(&"1.0.0".to_string()));
    assert_eq!(instance.metadata.get("region"), Some(&"us-west-2".to_string()));
}

#[tokio::test]
async fn test_service_registration_and_discovery() {
    let registry = setup_test_registry().await;
    
    // Register a service instance
    let instance = ServiceInstance::new(
        "test-service".to_string(),
        "localhost".to_string(),
        8080,
    );
    
    registry.register_instance(instance.clone()).await.expect("Failed to register instance");
    
    // Discover the service
    let instances = registry.get_service_instances("test-service").await.expect("Failed to get instances");
    
    assert_eq!(instances.len(), 1);
    assert_eq!(instances[0].name, "test-service");
    assert_eq!(instances[0].host, "localhost");
    assert_eq!(instances[0].port, 8080);
}

#[tokio::test]
async fn test_multiple_service_instances() {
    let registry = setup_test_registry().await;
    
    // Register multiple instances of the same service
    for i in 0..3 {
        let instance = ServiceInstance::new(
            "multi-service".to_string(),
            "localhost".to_string(),
            8080 + i,
        );
        
        registry.register_instance(instance).await.expect("Failed to register instance");
    }
    
    // Discover all instances
    let instances = registry.get_service_instances("multi-service").await.expect("Failed to get instances");
    
    assert_eq!(instances.len(), 3);
    
    // Check that all ports are present
    let ports: Vec<u16> = instances.iter().map(|i| i.port).collect();
    assert!(ports.contains(&8080));
    assert!(ports.contains(&8081));
    assert!(ports.contains(&8082));
}

#[tokio::test]
async fn test_service_deregistration() {
    let registry = setup_test_registry().await;
    
    // Register a service instance
    let instance = ServiceInstance::new(
        "temp-service".to_string(),
        "localhost".to_string(),
        9000,
    );
    
    registry.register_instance(instance.clone()).await.expect("Failed to register instance");
    
    // Verify it's registered
    let instances = registry.get_service_instances("temp-service").await.expect("Failed to get instances");
    assert_eq!(instances.len(), 1);
    
    // Deregister the instance
    registry.deregister_instance("temp-service", &instance.id).await.expect("Failed to deregister instance");
    
    // Verify it's gone
    let instances = registry.get_service_instances("temp-service").await.expect("Failed to get instances");
    assert_eq!(instances.len(), 0);
}

#[tokio::test]
async fn test_load_balancing_round_robin() {
    let registry = setup_test_registry().await;
    
    // Register multiple instances
    for i in 0..3 {
        let instance = ServiceInstance::new(
            "lb-service".to_string(),
            "localhost".to_string(),
            7000 + i,
        );
        
        registry.register_instance(instance).await.expect("Failed to register instance");
    }
    
    // Test round-robin load balancing
    let mut selected_ports = Vec::new();
    
    for _ in 0..6 {
        if let Some(instance) = registry.get_instance("lb-service", LoadBalancingStrategy::RoundRobin).await.expect("Failed to get instance") {
            selected_ports.push(instance.port);
        }
    }
    
    // Should cycle through all instances
    assert_eq!(selected_ports.len(), 6);
    
    // Each port should appear exactly twice
    for port in [7000, 7001, 7002] {
        let count = selected_ports.iter().filter(|&&p| p == port).count();
        assert_eq!(count, 2, "Port {} appeared {} times instead of 2", port, count);
    }
}

#[tokio::test]
async fn test_load_balancing_random() {
    let registry = setup_test_registry().await;
    
    // Register multiple instances
    for i in 0..3 {
        let instance = ServiceInstance::new(
            "random-service".to_string(),
            "localhost".to_string(),
            6000 + i,
        );
        
        registry.register_instance(instance).await.expect("Failed to register instance");
    }
    
    // Test random load balancing
    let mut selected_ports = Vec::new();
    
    for _ in 0..30 {
        if let Some(instance) = registry.get_instance("random-service", LoadBalancingStrategy::Random).await.expect("Failed to get instance") {
            selected_ports.push(instance.port);
        }
    }
    
    // Should have selected instances (randomness makes exact testing difficult)
    assert_eq!(selected_ports.len(), 30);
    
    // All selected ports should be valid
    for port in selected_ports {
        assert!(port >= 6000 && port <= 6002);
    }
}

#[tokio::test]
async fn test_weighted_round_robin() {
    let registry = setup_test_registry().await;
    
    // Register instances with different weights
    let instance1 = ServiceInstance::new(
        "weighted-service".to_string(),
        "localhost".to_string(),
        5000,
    ).with_weight(100);
    
    let instance2 = ServiceInstance::new(
        "weighted-service".to_string(),
        "localhost".to_string(),
        5001,
    ).with_weight(200);
    
    let instance3 = ServiceInstance::new(
        "weighted-service".to_string(),
        "localhost".to_string(),
        5002,
    ).with_weight(300);
    
    registry.register_instance(instance1).await.expect("Failed to register instance");
    registry.register_instance(instance2).await.expect("Failed to register instance");
    registry.register_instance(instance3).await.expect("Failed to register instance");
    
    // Test weighted round-robin
    let mut port_counts = HashMap::new();
    
    for _ in 0..60 {
        if let Some(instance) = registry.get_instance("weighted-service", LoadBalancingStrategy::WeightedRoundRobin).await.expect("Failed to get instance") {
            *port_counts.entry(instance.port).or_insert(0) += 1;
        }
    }
    
    // Higher weight instances should be selected more often
    let count_5000 = port_counts.get(&5000).unwrap_or(&0);
    let count_5001 = port_counts.get(&5001).unwrap_or(&0);
    let count_5002 = port_counts.get(&5002).unwrap_or(&0);
    
    // Instance with weight 300 should be selected most often
    assert!(count_5002 > count_5001);
    assert!(count_5001 > count_5000);
}

#[tokio::test]
async fn test_healthy_instances_only() {
    let registry = setup_test_registry().await;
    
    // Register instances
    let mut instance1 = ServiceInstance::new(
        "health-service".to_string(),
        "localhost".to_string(),
        4000,
    );
    instance1.healthy = true;
    
    let mut instance2 = ServiceInstance::new(
        "health-service".to_string(),
        "localhost".to_string(),
        4001,
    );
    instance2.healthy = false;
    
    registry.register_instance(instance1).await.expect("Failed to register instance");
    registry.register_instance(instance2).await.expect("Failed to register instance");
    
    // Get all instances
    let all_instances = registry.get_service_instances("health-service").await.expect("Failed to get instances");
    assert_eq!(all_instances.len(), 2);
    
    // Get only healthy instances
    let healthy_instances = registry.get_healthy_instances("health-service").await.expect("Failed to get healthy instances");
    assert_eq!(healthy_instances.len(), 1);
    assert_eq!(healthy_instances[0].port, 4000);
    
    // Load balancing should only return healthy instances
    if let Some(instance) = registry.get_instance("health-service", LoadBalancingStrategy::RoundRobin).await.expect("Failed to get instance") {
        assert_eq!(instance.port, 4000);
    } else {
        panic!("Should have returned a healthy instance");
    }
}

#[tokio::test]
async fn test_no_instances_available() {
    let registry = setup_test_registry().await;
    
    // Try to get instance for non-existent service
    let instance = registry.get_instance("non-existent-service", LoadBalancingStrategy::RoundRobin).await.expect("Failed to get instance");
    assert!(instance.is_none());
    
    // Try to get instances for non-existent service
    let instances = registry.get_service_instances("non-existent-service").await.expect("Failed to get instances");
    assert!(instances.is_empty());
}

#[tokio::test]
async fn test_service_discovery_client() {
    let registry = setup_test_registry().await;
    
    // Create a service discovery client
    let client = ServiceDiscoveryClient::new(
        registry.clone(),
        "client-service".to_string(),
        "localhost".to_string(),
        3000,
    );
    
    // Register the client service
    client.register().await.expect("Failed to register client");
    
    // Verify registration
    let instances = registry.get_service_instances("client-service").await.expect("Failed to get instances");
    assert_eq!(instances.len(), 1);
    assert_eq!(instances[0].name, "client-service");
    assert_eq!(instances[0].port, 3000);
    
    // Register a target service
    let target_instance = ServiceInstance::new(
        "target-service".to_string(),
        "localhost".to_string(),
        4000,
    );
    registry.register_instance(target_instance).await.expect("Failed to register target");
    
    // Discover the target service
    let discovered = client.discover_service("target-service", LoadBalancingStrategy::RoundRobin).await.expect("Failed to discover service");
    assert!(discovered.is_some());
    assert_eq!(discovered.unwrap().port, 4000);
    
    // Deregister the client
    client.deregister().await.expect("Failed to deregister client");
    
    // Verify deregistration
    let instances = registry.get_service_instances("client-service").await.expect("Failed to get instances");
    assert!(instances.is_empty());
}

#[tokio::test]
async fn test_service_discovery_stats() {
    let registry = setup_test_registry().await;
    
    // Register services
    for service_name in ["stats-service-1", "stats-service-2"] {
        for i in 0..2 {
            let mut instance = ServiceInstance::new(
                service_name.to_string(),
                "localhost".to_string(),
                2000 + i,
            );
            
            // Make one instance unhealthy
            if i == 1 {
                instance.healthy = false;
            }
            
            registry.register_instance(instance).await.expect("Failed to register instance");
        }
    }
    
    // Get stats
    let stats = registry.get_stats().await.expect("Failed to get stats");
    
    assert_eq!(stats.services_count, 2);
    assert_eq!(stats.total_instances, 4);
    assert_eq!(stats.healthy_instances, 2);
    assert_eq!(stats.unhealthy_instances, 2);
}

#[tokio::test]
async fn test_concurrent_service_operations() {
    let registry = setup_test_registry().await;
    
    let mut handles = vec![];
    
    // Spawn multiple concurrent registration tasks
    for i in 0..10 {
        let registry_clone = registry.clone();
        let handle = tokio::spawn(async move {
            let instance = ServiceInstance::new(
                format!("concurrent-service-{}", i),
                "localhost".to_string(),
                1000 + i,
            );
            
            registry_clone.register_instance(instance).await
        });
        handles.push(handle);
    }
    
    // Wait for all registrations to complete
    for handle in handles {
        handle.await.unwrap().expect("Failed to register instance");
    }
    
    // Verify all services are registered
    for i in 0..10 {
        let instances = registry.get_service_instances(&format!("concurrent-service-{}", i)).await.expect("Failed to get instances");
        assert_eq!(instances.len(), 1);
        assert_eq!(instances[0].port, 1000 + i as u16);
    }
}
