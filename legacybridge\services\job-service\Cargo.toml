[package]
name = "job-service"
version = "0.1.0"
edition = "2021"
description = "Job processing and orchestration service for LegacyBridge microservices architecture"

[dependencies]
# Shared library
legacybridge-shared = { path = "../shared" }

# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "timeout", "request-id"] }
hyper = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-cron-scheduler = "0.9"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json", "migrate"] }

# Redis for job queue and coordination
redis = { version = "0.24", features = ["tokio-comp", "connection-manager", "streams"] }

# UUID and time
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-opentelemetry = "0.21"
opentelemetry = "0.20"
opentelemetry-jaeger = "0.19"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Validation
validator = { version = "0.16", features = ["derive"] }

# Configuration
config = "0.13"
dotenvy = "0.15"

# Metrics
prometheus = "0.13"

# HTTP client for service communication
reqwest = { version = "0.11", features = ["json"] }

# Workflow and state machine
serde_yaml = "0.9"

# Distributed locking
redis-rs = "0.24"

[dev-dependencies]
tokio-test = "0.4"
testcontainers = "0.15"
axum-test = "14.0"
