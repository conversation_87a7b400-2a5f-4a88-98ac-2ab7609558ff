// Plain text parsing and generation
use super::{ParsedDocument, DocumentElement};
use legacybridge_shared::{ServiceError, ServiceResult};
use serde_json::Value;
use std::collections::HashMap;

/// Parse plain text content into structured document
pub fn parse_text(content: &str) -> ServiceResult<ParsedDocument> {
    let mut elements = Vec::new();
    let lines: Vec<&str> = content.lines().collect();
    let mut current_paragraph = String::new();

    for line in lines {
        let trimmed = line.trim();
        
        if trimmed.is_empty() {
            // Empty line - end current paragraph if any
            if !current_paragraph.trim().is_empty() {
                elements.push(DocumentElement::Paragraph {
                    text: current_paragraph.trim().to_string(),
                });
                current_paragraph.clear();
            }
        } else {
            // Add line to current paragraph
            if !current_paragraph.is_empty() {
                current_paragraph.push(' ');
            }
            current_paragraph.push_str(trimmed);
        }
    }

    // Add final paragraph if any
    if !current_paragraph.trim().is_empty() {
        elements.push(DocumentElement::Paragraph {
            text: current_paragraph.trim().to_string(),
        });
    }

    // Try to extract title from first line if it looks like a title
    let title = if let Some(DocumentElement::Paragraph { text }) = elements.first() {
        if text.len() < 100 && !text.contains('.') && !text.contains(',') {
            Some(text.clone())
        } else {
            None
        }
    } else {
        None
    };

    let mut metadata = HashMap::new();
    metadata.insert("format".to_string(), Value::String("text".to_string()));
    metadata.insert("line_count".to_string(), Value::Number(content.lines().count().into()));
    metadata.insert("paragraph_count".to_string(), Value::Number(elements.len().into()));

    Ok(ParsedDocument {
        title,
        content: content.to_string(),
        metadata,
        structure: elements,
    })
}

/// Generate plain text from structured document
pub fn generate_text(document: &ParsedDocument, options: Option<&Value>) -> ServiceResult<String> {
    let mut output = String::new();
    
    // Check options for formatting preferences
    let line_width = options
        .and_then(|opts| opts.get("line_width"))
        .and_then(|w| w.as_u64())
        .unwrap_or(80) as usize;
    
    let include_structure = options
        .and_then(|opts| opts.get("include_structure"))
        .and_then(|s| s.as_bool())
        .unwrap_or(false);

    for element in &document.structure {
        match element {
            DocumentElement::Heading { level, text } => {
                if include_structure {
                    // Add heading markers
                    let marker = match level {
                        1 => "=".repeat(text.len().min(line_width)),
                        2 => "-".repeat(text.len().min(line_width)),
                        _ => format!("{}. ", level),
                    };
                    
                    output.push_str(text);
                    output.push('\n');
                    if *level <= 2 {
                        output.push_str(&marker);
                        output.push('\n');
                    }
                } else {
                    output.push_str(text);
                    output.push('\n');
                }
                output.push('\n');
            }
            DocumentElement::Paragraph { text } => {
                let wrapped = wrap_text(text, line_width);
                output.push_str(&wrapped);
                output.push_str("\n\n");
            }
            DocumentElement::List { items, ordered } => {
                for (i, item) in items.iter().enumerate() {
                    let prefix = if *ordered {
                        format!("{}. ", i + 1)
                    } else {
                        "• ".to_string()
                    };
                    
                    let wrapped = wrap_text(item, line_width - prefix.len());
                    let indented = indent_text(&wrapped, &prefix, "  ");
                    output.push_str(&indented);
                    output.push('\n');
                }
                output.push('\n');
            }
            DocumentElement::Code { text, language: _ } => {
                // Indent code blocks
                for line in text.lines() {
                    output.push_str("    ");
                    output.push_str(line);
                    output.push('\n');
                }
                output.push('\n');
            }
            DocumentElement::Quote { text } => {
                let wrapped = wrap_text(text, line_width - 2);
                for line in wrapped.lines() {
                    output.push_str("> ");
                    output.push_str(line);
                    output.push('\n');
                }
                output.push('\n');
            }
            DocumentElement::Table { headers, rows } => {
                // Simple table formatting
                if !headers.is_empty() {
                    let col_widths = calculate_column_widths(headers, rows);
                    
                    // Headers
                    for (i, header) in headers.iter().enumerate() {
                        output.push_str(&format!("{:<width$}", header, width = col_widths[i]));
                        if i < headers.len() - 1 {
                            output.push_str(" | ");
                        }
                    }
                    output.push('\n');
                    
                    // Separator
                    for (i, &width) in col_widths.iter().enumerate() {
                        output.push_str(&"-".repeat(width));
                        if i < col_widths.len() - 1 {
                            output.push_str("-+-");
                        }
                    }
                    output.push('\n');
                    
                    // Rows
                    for row in rows {
                        for (i, cell) in row.iter().enumerate() {
                            if i < col_widths.len() {
                                output.push_str(&format!("{:<width$}", cell, width = col_widths[i]));
                                if i < row.len() - 1 && i < col_widths.len() - 1 {
                                    output.push_str(" | ");
                                }
                            }
                        }
                        output.push('\n');
                    }
                    output.push('\n');
                }
            }
            DocumentElement::Image { alt, url } => {
                output.push_str(&format!("[Image: {}] ({})\n\n", alt, url));
            }
            DocumentElement::Link { text, url } => {
                output.push_str(&format!("{} ({})", text, url));
            }
        }
    }

    Ok(output)
}

/// Wrap text to specified width
fn wrap_text(text: &str, width: usize) -> String {
    if width == 0 {
        return text.to_string();
    }

    let mut result = String::new();
    let mut current_line = String::new();
    
    for word in text.split_whitespace() {
        if current_line.len() + word.len() + 1 > width && !current_line.is_empty() {
            result.push_str(&current_line);
            result.push('\n');
            current_line.clear();
        }
        
        if !current_line.is_empty() {
            current_line.push(' ');
        }
        current_line.push_str(word);
    }
    
    if !current_line.is_empty() {
        result.push_str(&current_line);
    }
    
    result
}

/// Indent text with prefix for first line and continuation
fn indent_text(text: &str, first_prefix: &str, cont_prefix: &str) -> String {
    let mut result = String::new();
    let lines: Vec<&str> = text.lines().collect();
    
    for (i, line) in lines.iter().enumerate() {
        if i == 0 {
            result.push_str(first_prefix);
        } else {
            result.push_str(cont_prefix);
        }
        result.push_str(line);
        if i < lines.len() - 1 {
            result.push('\n');
        }
    }
    
    result
}

/// Calculate column widths for table formatting
fn calculate_column_widths(headers: &[String], rows: &[Vec<String>]) -> Vec<usize> {
    let mut widths = headers.iter().map(|h| h.len()).collect::<Vec<_>>();
    
    for row in rows {
        for (i, cell) in row.iter().enumerate() {
            if i < widths.len() {
                widths[i] = widths[i].max(cell.len());
            }
        }
    }
    
    widths
}
