// Authentication and authorization utilities
use crate::error::{ServiceError, ServiceResult};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON>ey, Encoding<PERSON><PERSON>, <PERSON>er, Validation};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: Uuid,           // Subject (user ID)
    pub username: String,    // Username
    pub roles: Vec<String>,  // User roles
    pub exp: i64,           // Expiration time
    pub iat: i64,           // Issued at
    pub iss: String,        // Issuer
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: Uuid,
    pub username: String,
    pub roles: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginResponse {
    pub token: String,
    pub refresh_token: String,
    pub expires_in: u64,
    pub user: UserInfo,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TokenValidationResponse {
    pub valid: bool,
    pub user_id: Option<Uuid>,
    pub roles: Vec<String>,
    pub expires_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RefreshTokenRequest {
    pub refresh_token: String,
}

pub struct JwtManager {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    issuer: String,
    token_expiry: Duration,
    refresh_token_expiry: Duration,
}

impl JwtManager {
    pub fn new(secret: &str, issuer: String) -> Self {
        Self {
            encoding_key: EncodingKey::from_secret(secret.as_ref()),
            decoding_key: DecodingKey::from_secret(secret.as_ref()),
            issuer,
            token_expiry: Duration::hours(1),
            refresh_token_expiry: Duration::days(7),
        }
    }

    pub fn generate_token(&self, user: &UserInfo) -> ServiceResult<String> {
        let now = Utc::now();
        let exp = now + self.token_expiry;

        let claims = Claims {
            sub: user.id,
            username: user.username.clone(),
            roles: user.roles.clone(),
            exp: exp.timestamp(),
            iat: now.timestamp(),
            iss: self.issuer.clone(),
        };

        encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(ServiceError::from)
    }

    pub fn generate_refresh_token(&self, user: &UserInfo) -> ServiceResult<String> {
        let now = Utc::now();
        let exp = now + self.refresh_token_expiry;

        let claims = Claims {
            sub: user.id,
            username: user.username.clone(),
            roles: user.roles.clone(),
            exp: exp.timestamp(),
            iat: now.timestamp(),
            iss: format!("{}-refresh", self.issuer),
        };

        encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(ServiceError::from)
    }

    pub fn validate_token(&self, token: &str) -> ServiceResult<Claims> {
        let validation = Validation::new(Algorithm::HS256);
        
        decode::<Claims>(token, &self.decoding_key, &validation)
            .map(|token_data| token_data.claims)
            .map_err(ServiceError::from)
    }

    pub fn validate_refresh_token(&self, token: &str) -> ServiceResult<Claims> {
        let mut validation = Validation::new(Algorithm::HS256);
        validation.set_issuer(&[format!("{}-refresh", self.issuer)]);
        
        decode::<Claims>(token, &self.decoding_key, &validation)
            .map(|token_data| token_data.claims)
            .map_err(ServiceError::from)
    }

    pub fn extract_bearer_token(auth_header: &str) -> ServiceResult<&str> {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            Ok(token)
        } else {
            Err(ServiceError::BadRequest("Invalid authorization header format".to_string()))
        }
    }
}

// Password hashing utilities
pub mod password {
    use argon2::{
        password_hash::{rand_core::OsRng, PasswordHash, PasswordHasher, PasswordVerifier, SaltString},
        Argon2,
    };
    use crate::error::{ServiceError, ServiceResult};

    pub fn hash_password(password: &str) -> ServiceResult<String> {
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        
        argon2
            .hash_password(password.as_bytes(), &salt)
            .map(|hash| hash.to_string())
            .map_err(|e| ServiceError::Internal(format!("Password hashing failed: {}", e)))
    }

    pub fn verify_password(password: &str, hash: &str) -> ServiceResult<bool> {
        let parsed_hash = PasswordHash::new(hash)
            .map_err(|e| ServiceError::Internal(format!("Invalid password hash: {}", e)))?;
        
        let argon2 = Argon2::default();
        
        match argon2.verify_password(password.as_bytes(), &parsed_hash) {
            Ok(()) => Ok(true),
            Err(_) => Ok(false),
        }
    }
}

// Role-based access control
pub mod rbac {
    use super::*;

    #[derive(Debug, Clone, PartialEq)]
    pub enum Role {
        Admin,
        User,
        Service,
    }

    impl Role {
        pub fn from_string(role: &str) -> Option<Self> {
            match role.to_lowercase().as_str() {
                "admin" => Some(Role::Admin),
                "user" => Some(Role::User),
                "service" => Some(Role::Service),
                _ => None,
            }
        }

        pub fn to_string(&self) -> String {
            match self {
                Role::Admin => "admin".to_string(),
                Role::User => "user".to_string(),
                Role::Service => "service".to_string(),
            }
        }
    }

    pub fn has_role(user_roles: &[String], required_role: Role) -> bool {
        user_roles.iter().any(|role| {
            Role::from_string(role).map_or(false, |r| r == required_role || r == Role::Admin)
        })
    }

    pub fn has_any_role(user_roles: &[String], required_roles: &[Role]) -> bool {
        required_roles.iter().any(|role| has_role(user_roles, role.clone()))
    }

    pub fn can_access_resource(user_roles: &[String], resource: &str, action: &str) -> bool {
        // Admin can access everything
        if has_role(user_roles, Role::Admin) {
            return true;
        }

        // Define resource-specific access rules
        match (resource, action) {
            ("files", "read") | ("files", "upload") => has_role(user_roles, Role::User),
            ("files", "delete") | ("files", "admin") => has_role(user_roles, Role::Admin),
            ("conversions", "create") | ("conversions", "read") => has_role(user_roles, Role::User),
            ("users", "read") => has_role(user_roles, Role::User),
            ("users", "create") | ("users", "update") | ("users", "delete") => has_role(user_roles, Role::Admin),
            ("system", _) => has_role(user_roles, Role::Admin),
            _ => false,
        }
    }
}
