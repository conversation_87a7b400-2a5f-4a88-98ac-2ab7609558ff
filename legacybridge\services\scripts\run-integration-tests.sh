#!/bin/bash

# Enterprise integration test runner for LegacyBridge microservices
# Tests service-to-service communication, API gateway, and enterprise features

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
SERVICES_DIR="$PROJECT_ROOT/legacybridge/services"
TEST_RESULTS_DIR="$SERVICES_DIR/test-results"

# Service URLs
AUTH_SERVICE_URL="http://localhost:3001"
CONVERSION_SERVICE_URL="http://localhost:3002"
FILE_SERVICE_URL="http://localhost:3003"
JOB_SERVICE_URL="http://localhost:3004"
KONG_GATEWAY_URL="http://localhost:8000"
KONG_ADMIN_URL="http://localhost:8001"

echo -e "${BLUE}🧪 LegacyBridge Enterprise Integration Tests${NC}"
echo "=============================================="

# Function to check if a service is running
check_service() {
    local service_name=$1
    local service_url=$2
    local health_path=${3:-"/health"}
    
    echo -n "  🔍 Checking $service_name... "
    
    if curl -f -s "$service_url$health_path" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Running${NC}"
        return 0
    else
        echo -e "${RED}❌ Not available${NC}"
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local service_url=$2
    local health_path=${3:-"/health"}
    local max_attempts=${4:-30}
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$service_url$health_path" > /dev/null 2>&1; then
            echo -e "  ${GREEN}✅ $service_name is ready${NC}"
            return 0
        fi
        
        echo "  ⏱️  Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        ((attempt++))
    done
    
    echo -e "  ${RED}❌ $service_name failed to become ready${NC}"
    return 1
}

# Function to start services if not running
start_services() {
    echo -e "${YELLOW}🚀 Starting services...${NC}"
    
    cd "$SERVICES_DIR"
    
    # Start infrastructure services
    echo "  📦 Starting infrastructure services..."
    docker-compose -f docker-compose.infrastructure.yml up -d
    
    # Wait for infrastructure to be ready
    echo "  ⏳ Waiting for infrastructure services..."
    sleep 10
    
    # Start Kong with custom plugins
    echo "  🌉 Starting Kong API Gateway..."
    docker-compose -f docker-compose.infrastructure.yml -f docker-compose.kong-custom.yml up -d kong
    
    # Wait for Kong to be ready
    wait_for_service "Kong Admin API" "$KONG_ADMIN_URL" "/status" 30
    
    # Configure Kong
    echo "  ⚙️  Configuring Kong..."
    if [ -f "./scripts/setup-kong.sh" ]; then
        chmod +x ./scripts/setup-kong.sh
        ./scripts/setup-kong.sh
    fi
    
    if [ -f "./scripts/setup-kong-advanced-routing.sh" ]; then
        chmod +x ./scripts/setup-kong-advanced-routing.sh
        ./scripts/setup-kong-advanced-routing.sh
    fi
    
    # Start microservices
    echo "  🏗️  Starting microservices..."
    
    # Start each service in the background
    cd auth-service && cargo run --release > ../logs/auth-service.log 2>&1 &
    cd ../conversion-service && cargo run --release > ../logs/conversion-service.log 2>&1 &
    cd ../file-service && cargo run --release > ../logs/file-service.log 2>&1 &
    cd ../job-service && cargo run --release > ../logs/job-service.log 2>&1 &
    
    cd "$SERVICES_DIR"
    
    # Wait for services to be ready
    wait_for_service "Auth Service" "$AUTH_SERVICE_URL" "/health" 60
    wait_for_service "Conversion Service" "$CONVERSION_SERVICE_URL" "/health" 60
    wait_for_service "File Service" "$FILE_SERVICE_URL" "/health" 60
    wait_for_service "Job Service" "$JOB_SERVICE_URL" "/health" 60
    wait_for_service "Kong Gateway" "$KONG_GATEWAY_URL" "/auth/health" 30
}

# Function to check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker is not running${NC}"
        exit 1
    fi
    echo -e "  ${GREEN}✅ Docker is running${NC}"
    
    # Check if Rust is installed
    if ! command -v cargo &> /dev/null; then
        echo -e "${RED}❌ Rust/Cargo is not installed${NC}"
        exit 1
    fi
    echo -e "  ${GREEN}✅ Rust/Cargo is available${NC}"
    
    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}❌ curl is not installed${NC}"
        exit 1
    fi
    echo -e "  ${GREEN}✅ curl is available${NC}"
    
    # Create necessary directories
    mkdir -p "$TEST_RESULTS_DIR"
    mkdir -p "$SERVICES_DIR/logs"
}

# Function to check service status
check_service_status() {
    echo -e "${YELLOW}🏥 Checking service health...${NC}"
    
    local all_healthy=true
    
    # Check infrastructure services
    if ! check_service "PostgreSQL" "postgresql://postgres:postgres@localhost:5432" ""; then
        all_healthy=false
    fi
    
    if ! check_service "Redis" "redis://localhost:6379" ""; then
        all_healthy=false
    fi
    
    if ! check_service "Kong Admin" "$KONG_ADMIN_URL" "/status"; then
        all_healthy=false
    fi
    
    if ! check_service "Kong Gateway" "$KONG_GATEWAY_URL" "/auth/health"; then
        all_healthy=false
    fi
    
    # Check microservices
    check_service "Auth Service" "$AUTH_SERVICE_URL" "/health" || all_healthy=false
    check_service "Conversion Service" "$CONVERSION_SERVICE_URL" "/health" || all_healthy=false
    check_service "File Service" "$FILE_SERVICE_URL" "/health" || all_healthy=false
    check_service "Job Service" "$JOB_SERVICE_URL" "/health" || all_healthy=false
    
    if [ "$all_healthy" = true ]; then
        echo -e "  ${GREEN}✅ All services are healthy${NC}"
        return 0
    else
        echo -e "  ${YELLOW}⚠️  Some services are not available${NC}"
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    echo -e "${BLUE}🧪 Running integration tests...${NC}"
    
    cd "$SERVICES_DIR"
    
    # Set environment variables for tests
    export AUTH_SERVICE_URL="$AUTH_SERVICE_URL"
    export CONVERSION_SERVICE_URL="$CONVERSION_SERVICE_URL"
    export FILE_SERVICE_URL="$FILE_SERVICE_URL"
    export JOB_SERVICE_URL="$JOB_SERVICE_URL"
    export KONG_GATEWAY_URL="$KONG_GATEWAY_URL"
    export RUST_LOG=info
    
    # Run the integration tests
    local test_result=0
    
    echo "  🔬 Running Rust integration tests..."
    if cargo test --test integration_tests --release -- --test-threads=1 > "$TEST_RESULTS_DIR/integration_test_output.txt" 2>&1; then
        echo -e "  ${GREEN}✅ Integration tests passed${NC}"
    else
        echo -e "  ${RED}❌ Integration tests failed${NC}"
        test_result=1
        
        # Show last 20 lines of test output
        echo -e "${YELLOW}Last 20 lines of test output:${NC}"
        tail -n 20 "$TEST_RESULTS_DIR/integration_test_output.txt"
    fi
    
    return $test_result
}

# Function to run performance tests
run_performance_tests() {
    echo -e "${BLUE}⚡ Running performance tests...${NC}"
    
    local perf_results="$TEST_RESULTS_DIR/performance_results.txt"
    echo "Performance Test Results - $(date)" > "$perf_results"
    echo "========================================" >> "$perf_results"
    
    # Test API Gateway performance
    echo "  📊 Testing API Gateway performance..."
    
    local start_time=$(date +%s.%N)
    local success_count=0
    local total_requests=100
    
    for i in $(seq 1 $total_requests); do
        if curl -f -s "$KONG_GATEWAY_URL/auth/health" > /dev/null 2>&1; then
            ((success_count++))
        fi
    done
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    local requests_per_second=$(echo "scale=2; $success_count / $duration" | bc)
    
    echo "API Gateway Performance:" >> "$perf_results"
    echo "  Requests: $success_count/$total_requests" >> "$perf_results"
    echo "  Duration: ${duration}s" >> "$perf_results"
    echo "  Requests/sec: $requests_per_second" >> "$perf_results"
    echo "" >> "$perf_results"
    
    echo -e "  ${GREEN}✅ Performance tests completed${NC}"
    echo "  📈 Results: $success_count/$total_requests requests successful"
    echo "  🚀 Performance: $requests_per_second req/sec"
}

# Function to generate test report
generate_test_report() {
    echo -e "${BLUE}📊 Generating test report...${NC}"
    
    local report_file="$TEST_RESULTS_DIR/integration_test_report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>LegacyBridge Integration Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .success { color: #28a745; }
        .failure { color: #dc3545; }
        .warning { color: #ffc107; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>LegacyBridge Integration Test Report</h1>
        <p>Generated on: $(date)</p>
        <p>Test Environment: Enterprise Integration</p>
    </div>
    
    <div class="section">
        <h2>Service Health Status</h2>
        <ul>
            <li>Auth Service: <span class="success">HEALTHY</span></li>
            <li>Conversion Service: <span class="success">HEALTHY</span></li>
            <li>File Service: <span class="success">HEALTHY</span></li>
            <li>Job Service: <span class="success">HEALTHY</span></li>
            <li>Kong Gateway: <span class="success">HEALTHY</span></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Integration Test Results</h2>
        <pre>$(cat "$TEST_RESULTS_DIR/integration_test_output.txt" 2>/dev/null || echo "Integration tests not run")</pre>
    </div>
    
    <div class="section">
        <h2>Performance Results</h2>
        <pre>$(cat "$TEST_RESULTS_DIR/performance_results.txt" 2>/dev/null || echo "Performance tests not run")</pre>
    </div>
</body>
</html>
EOF
    
    echo -e "  ${GREEN}✅ Test report generated: $report_file${NC}"
}

# Function to cleanup
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    
    # Kill background services
    pkill -f "cargo run" || true
    
    # Stop Docker services
    cd "$SERVICES_DIR"
    docker-compose -f docker-compose.infrastructure.yml -f docker-compose.kong-custom.yml down || true
}

# Main execution
main() {
    local test_result=0
    
    echo -e "${BLUE}Starting enterprise integration test suite...${NC}"
    echo ""
    
    # Setup
    check_prerequisites
    
    # Check if services are already running
    if ! check_service_status; then
        echo -e "${YELLOW}Some services are not running, starting them...${NC}"
        start_services
        echo ""
        
        # Verify services are now running
        if ! check_service_status; then
            echo -e "${RED}❌ Failed to start all required services${NC}"
            exit 1
        fi
    fi
    
    echo ""
    
    # Run tests
    if ! run_integration_tests; then
        test_result=1
    fi
    
    echo ""
    
    # Run performance tests
    run_performance_tests
    
    echo ""
    
    # Generate report
    generate_test_report
    
    echo ""
    echo "=================================================="
    
    if [ $test_result -eq 0 ]; then
        echo -e "${GREEN}🎉 All integration tests PASSED!${NC}"
        echo -e "${GREEN}✅ Service-to-service communication verified${NC}"
        echo -e "${GREEN}✅ API Gateway routing functional${NC}"
        echo -e "${GREEN}✅ Authentication flow working${NC}"
        echo -e "${GREEN}✅ Performance requirements met${NC}"
    else
        echo -e "${RED}❌ Some integration tests FAILED!${NC}"
        echo -e "${YELLOW}📋 Check test results in: $TEST_RESULTS_DIR${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}📊 Test artifacts:${NC}"
    echo "  📁 Results directory: $TEST_RESULTS_DIR"
    echo "  📄 Test report: $TEST_RESULTS_DIR/integration_test_report.html"
    echo "  📈 Performance results: $TEST_RESULTS_DIR/performance_results.txt"
    echo "  📋 Integration output: $TEST_RESULTS_DIR/integration_test_output.txt"
    
    # Cleanup on exit
    trap cleanup EXIT
    
    exit $test_result
}

# Handle Ctrl+C gracefully
trap cleanup INT

# Run main function
main "$@"
