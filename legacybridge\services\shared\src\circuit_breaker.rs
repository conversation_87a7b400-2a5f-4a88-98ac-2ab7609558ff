// Circuit breaker pattern implementation for resilient service communication
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::time::timeout;
use crate::error::{ServiceError, ServiceResult};

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum CircuitState {
    Closed,   // Normal operation
    Open,     // Failing fast
    HalfOpen, // Testing if service recovered
}

pub struct CircuitBreaker {
    state: Arc<Mutex<CircuitBreakerState>>,
    config: CircuitBreakerConfig,
}

struct CircuitBreakerState {
    state: CircuitState,
    failure_count: u32,
    last_failure_time: Option<Instant>,
    last_success_time: Option<Instant>,
    next_attempt: Option<Instant>,
    half_open_attempts: u32,
}

pub struct CircuitBreakerConfig {
    pub failure_threshold: u32,
    pub recovery_timeout: Duration,
    pub request_timeout: Duration,
    pub half_open_max_calls: u32,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 5,
            recovery_timeout: Duration::from_secs(60),
            request_timeout: Duration::from_secs(10),
            half_open_max_calls: 3,
        }
    }
}

#[derive(Debug)]
pub enum CircuitBreakerError {
    CircuitOpen,
    Timeout,
    ServiceError(ServiceError),
}

impl From<CircuitBreakerError> for ServiceError {
    fn from(error: CircuitBreakerError) -> Self {
        match error {
            CircuitBreakerError::CircuitOpen => ServiceError::CircuitBreakerOpen,
            CircuitBreakerError::Timeout => ServiceError::Timeout,
            CircuitBreakerError::ServiceError(e) => e,
        }
    }
}

impl CircuitBreaker {
    pub fn new(config: CircuitBreakerConfig) -> Self {
        Self {
            state: Arc::new(Mutex::new(CircuitBreakerState {
                state: CircuitState::Closed,
                failure_count: 0,
                last_failure_time: None,
                last_success_time: None,
                next_attempt: None,
                half_open_attempts: 0,
            })),
            config,
        }
    }

    pub async fn call<F, Fut, T>(&self, operation: F) -> ServiceResult<T>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = ServiceResult<T>>,
    {
        // Check if we should attempt the call
        if !self.should_attempt_call() {
            return Err(ServiceError::CircuitBreakerOpen);
        }

        // Execute the operation with timeout
        let result = timeout(self.config.request_timeout, operation()).await;

        match result {
            Ok(Ok(value)) => {
                self.on_success();
                Ok(value)
            }
            Ok(Err(e)) => {
                self.on_failure();
                Err(e)
            }
            Err(_) => {
                self.on_failure();
                Err(ServiceError::Timeout)
            }
        }
    }

    fn should_attempt_call(&self) -> bool {
        let mut state = self.state.lock().unwrap();
        let now = Instant::now();

        match state.state {
            CircuitState::Closed => true,
            CircuitState::Open => {
                if let Some(next_attempt) = state.next_attempt {
                    if now >= next_attempt {
                        state.state = CircuitState::HalfOpen;
                        state.half_open_attempts = 0;
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            }
            CircuitState::HalfOpen => {
                state.half_open_attempts < self.config.half_open_max_calls
            }
        }
    }

    fn on_success(&self) {
        let mut state = self.state.lock().unwrap();
        state.failure_count = 0;
        state.last_success_time = Some(Instant::now());
        state.state = CircuitState::Closed;
        state.next_attempt = None;
        state.half_open_attempts = 0;
    }

    fn on_failure(&self) {
        let mut state = self.state.lock().unwrap();
        state.failure_count += 1;
        state.last_failure_time = Some(Instant::now());

        match state.state {
            CircuitState::Closed => {
                if state.failure_count >= self.config.failure_threshold {
                    state.state = CircuitState::Open;
                    state.next_attempt = Some(Instant::now() + self.config.recovery_timeout);
                }
            }
            CircuitState::HalfOpen => {
                state.state = CircuitState::Open;
                state.next_attempt = Some(Instant::now() + self.config.recovery_timeout);
                state.half_open_attempts = 0;
            }
            CircuitState::Open => {
                // Already open, just update the next attempt time
                state.next_attempt = Some(Instant::now() + self.config.recovery_timeout);
            }
        }
    }

    pub fn get_state(&self) -> CircuitState {
        self.state.lock().unwrap().state
    }

    pub fn get_stats(&self) -> CircuitBreakerStats {
        let state = self.state.lock().unwrap();
        CircuitBreakerStats {
            state: state.state,
            failure_count: state.failure_count,
            last_failure_time: state.last_failure_time,
            last_success_time: state.last_success_time,
            half_open_attempts: state.half_open_attempts,
        }
    }

    pub fn force_open(&self) {
        let mut state = self.state.lock().unwrap();
        state.state = CircuitState::Open;
        state.next_attempt = Some(Instant::now() + self.config.recovery_timeout);
    }

    pub fn force_close(&self) {
        let mut state = self.state.lock().unwrap();
        state.state = CircuitState::Closed;
        state.failure_count = 0;
        state.next_attempt = None;
        state.half_open_attempts = 0;
    }
}

#[derive(Debug)]
pub struct CircuitBreakerStats {
    pub state: CircuitState,
    pub failure_count: u32,
    pub last_failure_time: Option<Instant>,
    pub last_success_time: Option<Instant>,
    pub half_open_attempts: u32,
}

// Circuit breaker registry for managing multiple circuit breakers
use std::collections::HashMap;

pub struct CircuitBreakerRegistry {
    breakers: Arc<Mutex<HashMap<String, Arc<CircuitBreaker>>>>,
    default_config: CircuitBreakerConfig,
}

impl CircuitBreakerRegistry {
    pub fn new(default_config: CircuitBreakerConfig) -> Self {
        Self {
            breakers: Arc::new(Mutex::new(HashMap::new())),
            default_config,
        }
    }

    pub fn get_or_create(&self, name: &str) -> Arc<CircuitBreaker> {
        let mut breakers = self.breakers.lock().unwrap();
        
        if let Some(breaker) = breakers.get(name) {
            breaker.clone()
        } else {
            let breaker = Arc::new(CircuitBreaker::new(self.default_config.clone()));
            breakers.insert(name.to_string(), breaker.clone());
            breaker
        }
    }

    pub fn get_or_create_with_config(&self, name: &str, config: CircuitBreakerConfig) -> Arc<CircuitBreaker> {
        let mut breakers = self.breakers.lock().unwrap();
        
        if let Some(breaker) = breakers.get(name) {
            breaker.clone()
        } else {
            let breaker = Arc::new(CircuitBreaker::new(config));
            breakers.insert(name.to_string(), breaker.clone());
            breaker
        }
    }

    pub fn get_all_stats(&self) -> HashMap<String, CircuitBreakerStats> {
        let breakers = self.breakers.lock().unwrap();
        breakers
            .iter()
            .map(|(name, breaker)| (name.clone(), breaker.get_stats()))
            .collect()
    }

    pub fn force_open_all(&self) {
        let breakers = self.breakers.lock().unwrap();
        for breaker in breakers.values() {
            breaker.force_open();
        }
    }

    pub fn force_close_all(&self) {
        let breakers = self.breakers.lock().unwrap();
        for breaker in breakers.values() {
            breaker.force_close();
        }
    }
}

impl Clone for CircuitBreakerConfig {
    fn clone(&self) -> Self {
        Self {
            failure_threshold: self.failure_threshold,
            recovery_timeout: self.recovery_timeout,
            request_timeout: self.request_timeout,
            half_open_max_calls: self.half_open_max_calls,
        }
    }
}
