# File Service Auto-Scaling Configuration
# Horizontal and Vertical Pod Autoscalers for the File Management Service

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: file-service-hpa
  namespace: legacybridge
  labels:
    app: file-service
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: file-service
  minReplicas: 2
  maxReplicas: 8
  metrics:
  # CPU-based scaling
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 75
  # Memory-based scaling
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  # Custom metrics for file service
  - type: Pods
    pods:
      metric:
        name: file_upload_requests_per_second
      target:
        type: AverageValue
        averageValue: "20"
  - type: Pods
    pods:
      metric:
        name: active_file_operations
      target:
        type: AverageValue
        averageValue: "10"
  - type: Pods
    pods:
      metric:
        name: s3_operation_queue_length
      target:
        type: AverageValue
        averageValue: "15"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min

---
# Vertical Pod Autoscaler for File Service
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: file-service-vpa
  namespace: legacybridge
  labels:
    app: file-service
    component: autoscaling
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: file-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: file-service
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 2000m
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

---
# Pod Disruption Budget for File Service
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: file-service-pdb
  namespace: legacybridge
  labels:
    app: file-service
    component: availability
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: file-service

---
# ServiceMonitor for File Service Metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: file-service-metrics
  namespace: legacybridge
  labels:
    app: file-service
    component: monitoring
spec:
  selector:
    matchLabels:
      app: file-service
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    honorLabels: true

---
# Custom Metrics Configuration for File Service
apiVersion: v1
kind: ConfigMap
metadata:
  name: file-service-metrics-config
  namespace: legacybridge
  labels:
    app: file-service
    component: metrics
data:
  metrics.yaml: |
    metrics:
      file_upload_requests_per_second:
        description: "Number of file upload requests per second"
        type: "gauge"
        labels: ["file_type", "status"]
      active_file_operations:
        description: "Number of currently active file operations"
        type: "gauge"
        labels: ["operation_type"]
      s3_operation_queue_length:
        description: "Number of S3 operations in queue"
        type: "gauge"
      file_operation_duration_seconds:
        description: "Time taken for file operations"
        type: "histogram"
        labels: ["operation_type", "file_type", "status"]
        buckets: [0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0]
      file_size_bytes:
        description: "Size of files being processed"
        type: "histogram"
        buckets: [1024, 10240, 102400, 1048576, 10485760, 104857600, 1073741824]
      s3_operation_success_rate:
        description: "S3 operation success rate"
        type: "gauge"
        labels: ["operation_type"]
      database_file_metadata_operations:
        description: "Database operations for file metadata"
        type: "counter"
        labels: ["operation", "status"]

---
# Prometheus Rules for File Service
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: file-service-alerts
  namespace: legacybridge
  labels:
    app: file-service
    component: alerting
spec:
  groups:
  - name: file-service.rules
    rules:
    # High upload rate alert
    - alert: FileServiceHighUploadRate
      expr: file_upload_requests_per_second > 100
      for: 2m
      labels:
        severity: warning
        service: file-service
      annotations:
        summary: "File service experiencing high upload rate"
        description: "File service has {{ $value }} uploads per second"
    
    # S3 operation failures
    - alert: FileServiceS3OperationFailures
      expr: rate(s3_operation_success_rate[5m]) < 0.95
      for: 3m
      labels:
        severity: critical
        service: file-service
      annotations:
        summary: "File service S3 operation failures"
        description: "S3 operation success rate is {{ $value | humanizePercentage }}"
    
    # High queue length alert
    - alert: FileServiceHighQueueLength
      expr: s3_operation_queue_length > 100
      for: 2m
      labels:
        severity: warning
        service: file-service
      annotations:
        summary: "File service S3 operation queue backing up"
        description: "S3 operation queue has {{ $value }} pending operations"
    
    # Slow file operations
    - alert: FileServiceSlowOperations
      expr: histogram_quantile(0.95, rate(file_operation_duration_seconds_bucket[5m])) > 30
      for: 5m
      labels:
        severity: warning
        service: file-service
      annotations:
        summary: "File service slow operations"
        description: "95th percentile file operation time is {{ $value }}s"
    
    # Database connection issues
    - alert: FileServiceDatabaseIssues
      expr: rate(database_file_metadata_operations_total{status="error"}[5m]) > 0.1
      for: 2m
      labels:
        severity: warning
        service: file-service
      annotations:
        summary: "File service database connection issues"
        description: "File service is experiencing database errors"
    
    # Service down alert
    - alert: FileServiceDown
      expr: up{job="file-service"} == 0
      for: 1m
      labels:
        severity: critical
        service: file-service
      annotations:
        summary: "File service is down"
        description: "File service has been down for more than 1 minute"

---
# Network Policy for File Service
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: file-service-network-policy
  namespace: legacybridge
  labels:
    app: file-service
    component: security
spec:
  podSelector:
    matchLabels:
      app: file-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from Kong Gateway
  - from:
    - podSelector:
        matchLabels:
          app: kong-gateway
    ports:
    - protocol: TCP
      port: 3003
  # Allow traffic from other microservices
  - from:
    - podSelector:
        matchLabels:
          app: conversion-service
    - podSelector:
        matchLabels:
          app: job-service
    ports:
    - protocol: TCP
      port: 3003
  # Allow monitoring traffic
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  egress:
  # Allow traffic to PostgreSQL for metadata
  - to:
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 5432
  # Allow traffic to S3/MinIO
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 9000
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
