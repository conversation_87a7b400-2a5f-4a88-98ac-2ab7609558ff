// Service-to-service communication client with circuit breaker support
use crate::circuit_breaker::{CircuitBreaker, CircuitBreakerConfig};
use crate::error::{ServiceError, ServiceResult};
use crate::auth::UserInfo;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Duration;

pub struct ServiceClient {
    client: Client,
    base_url: String,
    circuit_breaker: Arc<CircuitBreaker>,
    auth_token: Option<String>,
}

impl ServiceClient {
    pub fn new(base_url: String) -> Self {
        let circuit_config = CircuitBreakerConfig::default();
        
        Self {
            client: Client::builder()
                .timeout(Duration::from_secs(30))
                .build()
                .unwrap(),
            base_url,
            circuit_breaker: Arc::new(CircuitBreaker::new(circuit_config)),
            auth_token: None,
        }
    }

    pub fn with_circuit_breaker_config(base_url: String, circuit_config: CircuitBreakerConfig) -> Self {
        Self {
            client: Client::builder()
                .timeout(Duration::from_secs(30))
                .build()
                .unwrap(),
            base_url,
            circuit_breaker: Arc::new(CircuitBreaker::new(circuit_config)),
            auth_token: None,
        }
    }

    pub fn with_auth_token(mut self, token: String) -> Self {
        self.auth_token = Some(token);
        self
    }

    pub async fn get<T>(&self, path: &str) -> ServiceResult<T>
    where
        T: for<'de> Deserialize<'de>,
    {
        let url = format!("{}{}", self.base_url, path);
        
        self.circuit_breaker.call(|| async {
            let mut request = self.client.get(&url);
            
            if let Some(token) = &self.auth_token {
                request = request.bearer_auth(token);
            }
            
            let response = request.send().await?;
            
            if response.status().is_success() {
                let data = response.json::<T>().await?;
                Ok(data)
            } else {
                let status = response.status();
                let error_text = response.text().await.unwrap_or_default();
                Err(ServiceError::HttpClient(reqwest::Error::from(
                    reqwest::Error::from(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("HTTP {}: {}", status, error_text)
                    ))
                )))
            }
        }).await
    }

    pub async fn post<T, R>(&self, path: &str, body: &T) -> ServiceResult<R>
    where
        T: Serialize,
        R: for<'de> Deserialize<'de>,
    {
        let url = format!("{}{}", self.base_url, path);
        
        self.circuit_breaker.call(|| async {
            let mut request = self.client.post(&url).json(body);
            
            if let Some(token) = &self.auth_token {
                request = request.bearer_auth(token);
            }
            
            let response = request.send().await?;
            
            if response.status().is_success() {
                let data = response.json::<R>().await?;
                Ok(data)
            } else {
                let status = response.status();
                let error_text = response.text().await.unwrap_or_default();
                Err(ServiceError::HttpClient(reqwest::Error::from(
                    reqwest::Error::from(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("HTTP {}: {}", status, error_text)
                    ))
                )))
            }
        }).await
    }

    pub async fn put<T, R>(&self, path: &str, body: &T) -> ServiceResult<R>
    where
        T: Serialize,
        R: for<'de> Deserialize<'de>,
    {
        let url = format!("{}{}", self.base_url, path);
        
        self.circuit_breaker.call(|| async {
            let mut request = self.client.put(&url).json(body);
            
            if let Some(token) = &self.auth_token {
                request = request.bearer_auth(token);
            }
            
            let response = request.send().await?;
            
            if response.status().is_success() {
                let data = response.json::<R>().await?;
                Ok(data)
            } else {
                let status = response.status();
                let error_text = response.text().await.unwrap_or_default();
                Err(ServiceError::HttpClient(reqwest::Error::from(
                    reqwest::Error::from(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("HTTP {}: {}", status, error_text)
                    ))
                )))
            }
        }).await
    }

    pub async fn delete(&self, path: &str) -> ServiceResult<()> {
        let url = format!("{}{}", self.base_url, path);
        
        self.circuit_breaker.call(|| async {
            let mut request = self.client.delete(&url);
            
            if let Some(token) = &self.auth_token {
                request = request.bearer_auth(token);
            }
            
            let response = request.send().await?;
            
            if response.status().is_success() {
                Ok(())
            } else {
                let status = response.status();
                let error_text = response.text().await.unwrap_or_default();
                Err(ServiceError::HttpClient(reqwest::Error::from(
                    reqwest::Error::from(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("HTTP {}: {}", status, error_text)
                    ))
                )))
            }
        }).await
    }

    pub fn get_circuit_breaker_stats(&self) -> crate::circuit_breaker::CircuitBreakerStats {
        self.circuit_breaker.get_stats()
    }
}

// Specific service clients
pub struct AuthServiceClient {
    client: ServiceClient,
}

impl AuthServiceClient {
    pub fn new(base_url: String) -> Self {
        Self {
            client: ServiceClient::new(base_url),
        }
    }

    pub async fn validate_token(&self, token: &str) -> ServiceResult<crate::auth::TokenValidationResponse> {
        #[derive(Serialize)]
        struct ValidateRequest {
            token: String,
        }

        self.client.post("/validate", &ValidateRequest {
            token: token.to_string(),
        }).await
    }

    pub async fn get_user_info(&self, user_id: uuid::Uuid) -> ServiceResult<UserInfo> {
        self.client.get(&format!("/users/{}", user_id)).await
    }
}

pub struct ConversionServiceClient {
    client: ServiceClient,
}

impl ConversionServiceClient {
    pub fn new(base_url: String) -> Self {
        Self {
            client: ServiceClient::new(base_url),
        }
    }

    pub fn with_auth_token(mut self, token: String) -> Self {
        self.client = self.client.with_auth_token(token);
        self
    }

    pub async fn convert_document(&self, request: &crate::types::ConversionRequest) -> ServiceResult<crate::types::ConversionResponse> {
        self.client.post("/convert", request).await
    }

    pub async fn get_conversion_status(&self, job_id: &str) -> ServiceResult<crate::types::JobStatusResponse> {
        self.client.get(&format!("/convert/{}", job_id)).await
    }

    pub async fn get_conversion_result(&self, job_id: &str) -> ServiceResult<crate::types::ConversionResult> {
        self.client.get(&format!("/convert/{}/result", job_id)).await
    }
}

pub struct FileServiceClient {
    client: ServiceClient,
}

impl FileServiceClient {
    pub fn new(base_url: String) -> Self {
        Self {
            client: ServiceClient::new(base_url),
        }
    }

    pub fn with_auth_token(mut self, token: String) -> Self {
        self.client = self.client.with_auth_token(token);
        self
    }

    pub async fn upload_file(&self, content: Vec<u8>, filename: &str, content_type: &str) -> ServiceResult<crate::types::FileMetadata> {
        // For file uploads, we need to use multipart form data
        // This is a simplified version - in practice, you'd use reqwest's multipart support
        let url = format!("{}/files", self.client.base_url);
        
        self.client.circuit_breaker.call(|| async {
            let form = reqwest::multipart::Form::new()
                .part("file", reqwest::multipart::Part::bytes(content)
                    .file_name(filename.to_string())
                    .mime_str(content_type).unwrap_or(mime::APPLICATION_OCTET_STREAM));

            let mut request = self.client.client.post(&url).multipart(form);
            
            if let Some(token) = &self.client.auth_token {
                request = request.bearer_auth(token);
            }
            
            let response = request.send().await?;
            
            if response.status().is_success() {
                let data = response.json::<crate::types::FileMetadata>().await?;
                Ok(data)
            } else {
                let status = response.status();
                let error_text = response.text().await.unwrap_or_default();
                Err(ServiceError::HttpClient(reqwest::Error::from(
                    reqwest::Error::from(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("HTTP {}: {}", status, error_text)
                    ))
                )))
            }
        }).await
    }

    pub async fn get_file_metadata(&self, file_id: uuid::Uuid) -> ServiceResult<crate::types::FileMetadata> {
        self.client.get(&format!("/files/{}/metadata", file_id)).await
    }

    pub async fn delete_file(&self, file_id: uuid::Uuid) -> ServiceResult<()> {
        self.client.delete(&format!("/files/{}", file_id)).await
    }
}
