// Service Discovery for LegacyBridge Microservices
// Provides automatic service registration, health checking, and load balancing

use crate::cache::CacheManager;
use crate::error::{ServiceError, ServiceResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::time::interval;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceInstance {
    pub id: String,
    pub name: String,
    pub host: String,
    pub port: u16,
    pub health_check_url: String,
    pub metadata: HashMap<String, String>,
    pub registered_at: u64,
    pub last_health_check: u64,
    pub healthy: bool,
    pub weight: u32,
}

impl ServiceInstance {
    pub fn new(name: String, host: String, port: u16) -> Self {
        let id = format!("{}:{}:{}", name, host, port);
        let health_check_url = format!("http://{}:{}/health", host, port);
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        Self {
            id,
            name,
            host,
            port,
            health_check_url,
            metadata: HashMap::new(),
            registered_at: now,
            last_health_check: now,
            healthy: true,
            weight: 100,
        }
    }

    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    pub fn with_weight(mut self, weight: u32) -> Self {
        self.weight = weight;
        self
    }

    pub fn endpoint(&self) -> String {
        format!("http://{}:{}", self.host, self.port)
    }
}

pub struct ServiceRegistry {
    cache: Arc<CacheManager>,
    health_check_interval: Duration,
    health_check_timeout: Duration,
    unhealthy_threshold: u32,
}

impl ServiceRegistry {
    pub fn new(
        redis_url: &str,
        health_check_interval: Duration,
        health_check_timeout: Duration,
    ) -> ServiceResult<Self> {
        let cache = Arc::new(CacheManager::new(redis_url, Duration::from_secs(300))?);
        
        Ok(Self {
            cache,
            health_check_interval,
            health_check_timeout,
            unhealthy_threshold: 3,
        })
    }

    /// Register a service instance
    pub async fn register_instance(&self, instance: ServiceInstance) -> ServiceResult<()> {
        let key = format!("service:{}:{}", instance.name, instance.id);
        self.cache.set(&key, &instance).await?;

        // Add to service list
        let service_list_key = format!("service_list:{}", instance.name);
        let mut instances = self.get_service_instances(&instance.name).await?;
        
        // Remove existing instance with same ID
        instances.retain(|i| i.id != instance.id);
        instances.push(instance);

        self.cache.set(&service_list_key, &instances).await?;
        
        tracing::info!("Registered service instance: {}", instances.last().unwrap().id);
        Ok(())
    }

    /// Deregister a service instance
    pub async fn deregister_instance(&self, service_name: &str, instance_id: &str) -> ServiceResult<()> {
        let key = format!("service:{}:{}", service_name, instance_id);
        self.cache.delete(&key).await?;

        // Remove from service list
        let service_list_key = format!("service_list:{}", service_name);
        let mut instances = self.get_service_instances(service_name).await?;
        instances.retain(|i| i.id != instance_id);
        self.cache.set(&service_list_key, &instances).await?;

        tracing::info!("Deregistered service instance: {}", instance_id);
        Ok(())
    }

    /// Get all instances of a service
    pub async fn get_service_instances(&self, service_name: &str) -> ServiceResult<Vec<ServiceInstance>> {
        let service_list_key = format!("service_list:{}", service_name);
        
        match self.cache.get::<Vec<ServiceInstance>>(&service_list_key).await? {
            Some(instances) => Ok(instances),
            None => Ok(Vec::new()),
        }
    }

    /// Get healthy instances of a service
    pub async fn get_healthy_instances(&self, service_name: &str) -> ServiceResult<Vec<ServiceInstance>> {
        let instances = self.get_service_instances(service_name).await?;
        Ok(instances.into_iter().filter(|i| i.healthy).collect())
    }

    /// Get a service instance using load balancing
    pub async fn get_instance(&self, service_name: &str, strategy: LoadBalancingStrategy) -> ServiceResult<Option<ServiceInstance>> {
        let healthy_instances = self.get_healthy_instances(service_name).await?;
        
        if healthy_instances.is_empty() {
            return Ok(None);
        }

        let selected = match strategy {
            LoadBalancingStrategy::RoundRobin => {
                let counter_key = format!("lb_counter:{}", service_name);
                let counter = self.cache.increment(&counter_key).await? as usize;
                let index = counter % healthy_instances.len();
                healthy_instances.get(index).cloned()
            }
            LoadBalancingStrategy::Random => {
                use rand::Rng;
                let mut rng = rand::thread_rng();
                let index = rng.gen_range(0..healthy_instances.len());
                healthy_instances.get(index).cloned()
            }
            LoadBalancingStrategy::WeightedRoundRobin => {
                self.weighted_round_robin_selection(&healthy_instances, service_name).await?
            }
            LoadBalancingStrategy::LeastConnections => {
                // For now, use round robin as a fallback
                // In a real implementation, you'd track active connections
                let counter_key = format!("lb_counter:{}", service_name);
                let counter = self.cache.increment(&counter_key).await? as usize;
                let index = counter % healthy_instances.len();
                healthy_instances.get(index).cloned()
            }
        };

        Ok(selected)
    }

    async fn weighted_round_robin_selection(
        &self,
        instances: &[ServiceInstance],
        service_name: &str,
    ) -> ServiceResult<Option<ServiceInstance>> {
        let total_weight: u32 = instances.iter().map(|i| i.weight).sum();
        if total_weight == 0 {
            return Ok(instances.first().cloned());
        }

        let counter_key = format!("weighted_counter:{}", service_name);
        let counter = self.cache.increment(&counter_key).await? as u32;
        let target_weight = counter % total_weight;

        let mut current_weight = 0;
        for instance in instances {
            current_weight += instance.weight;
            if current_weight > target_weight {
                return Ok(Some(instance.clone()));
            }
        }

        Ok(instances.first().cloned())
    }

    /// Start health checking background task
    pub async fn start_health_checker(&self) -> ServiceResult<()> {
        let cache = self.cache.clone();
        let interval_duration = self.health_check_interval;
        let timeout = self.health_check_timeout;
        let unhealthy_threshold = self.unhealthy_threshold;

        tokio::spawn(async move {
            let mut interval = interval(interval_duration);
            let client = reqwest::Client::builder()
                .timeout(timeout)
                .build()
                .expect("Failed to create HTTP client");

            loop {
                interval.tick().await;

                // Get all service names
                if let Ok(service_names) = Self::get_all_service_names(&cache).await {
                    for service_name in service_names {
                        if let Ok(instances) = Self::get_service_instances_static(&cache, &service_name).await {
                            for mut instance in instances {
                                let health_status = Self::check_instance_health(&client, &instance).await;
                                
                                let now = SystemTime::now()
                                    .duration_since(UNIX_EPOCH)
                                    .unwrap()
                                    .as_secs();
                                
                                instance.last_health_check = now;
                                
                                match health_status {
                                    Ok(true) => {
                                        if !instance.healthy {
                                            tracing::info!("Service instance {} is now healthy", instance.id);
                                        }
                                        instance.healthy = true;
                                    }
                                    Ok(false) | Err(_) => {
                                        if instance.healthy {
                                            tracing::warn!("Service instance {} is now unhealthy", instance.id);
                                        }
                                        instance.healthy = false;
                                    }
                                }

                                // Update instance in cache
                                let key = format!("service:{}:{}", instance.name, instance.id);
                                let _ = cache.set(&key, &instance).await;
                            }

                            // Update service list
                            let service_list_key = format!("service_list:{}", service_name);
                            let _ = cache.set(&service_list_key, &instances).await;
                        }
                    }
                }
            }
        });

        Ok(())
    }

    async fn get_all_service_names(cache: &CacheManager) -> ServiceResult<Vec<String>> {
        let keys = cache.get_keys("service_list:*").await?;
        let service_names = keys
            .into_iter()
            .filter_map(|key| key.strip_prefix("service_list:").map(|s| s.to_string()))
            .collect();
        Ok(service_names)
    }

    async fn get_service_instances_static(
        cache: &CacheManager,
        service_name: &str,
    ) -> ServiceResult<Vec<ServiceInstance>> {
        let service_list_key = format!("service_list:{}", service_name);
        match cache.get::<Vec<ServiceInstance>>(&service_list_key).await? {
            Some(instances) => Ok(instances),
            None => Ok(Vec::new()),
        }
    }

    async fn check_instance_health(
        client: &reqwest::Client,
        instance: &ServiceInstance,
    ) -> ServiceResult<bool> {
        match client.get(&instance.health_check_url).send().await {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => Ok(false),
        }
    }

    /// Get service discovery statistics
    pub async fn get_stats(&self) -> ServiceResult<ServiceDiscoveryStats> {
        let service_names = Self::get_all_service_names(&self.cache).await?;
        let mut total_instances = 0;
        let mut healthy_instances = 0;
        let mut services_count = service_names.len();

        for service_name in &service_names {
            let instances = self.get_service_instances(service_name).await?;
            total_instances += instances.len();
            healthy_instances += instances.iter().filter(|i| i.healthy).count();
        }

        Ok(ServiceDiscoveryStats {
            services_count,
            total_instances,
            healthy_instances,
            unhealthy_instances: total_instances - healthy_instances,
        })
    }
}

#[derive(Debug, Clone)]
pub enum LoadBalancingStrategy {
    RoundRobin,
    Random,
    WeightedRoundRobin,
    LeastConnections,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ServiceDiscoveryStats {
    pub services_count: usize,
    pub total_instances: usize,
    pub healthy_instances: usize,
    pub unhealthy_instances: usize,
}

/// Service discovery client for microservices
pub struct ServiceDiscoveryClient {
    registry: Arc<ServiceRegistry>,
    instance: ServiceInstance,
}

impl ServiceDiscoveryClient {
    pub fn new(
        registry: Arc<ServiceRegistry>,
        service_name: String,
        host: String,
        port: u16,
    ) -> Self {
        let instance = ServiceInstance::new(service_name, host, port);
        
        Self {
            registry,
            instance,
        }
    }

    pub fn with_metadata(mut self, metadata: HashMap<String, String>) -> Self {
        self.instance.metadata = metadata;
        self
    }

    /// Register this service instance
    pub async fn register(&self) -> ServiceResult<()> {
        self.registry.register_instance(self.instance.clone()).await
    }

    /// Deregister this service instance
    pub async fn deregister(&self) -> ServiceResult<()> {
        self.registry
            .deregister_instance(&self.instance.name, &self.instance.id)
            .await
    }

    /// Discover another service
    pub async fn discover_service(
        &self,
        service_name: &str,
        strategy: LoadBalancingStrategy,
    ) -> ServiceResult<Option<ServiceInstance>> {
        self.registry.get_instance(service_name, strategy).await
    }

    /// Get all instances of a service
    pub async fn get_service_instances(&self, service_name: &str) -> ServiceResult<Vec<ServiceInstance>> {
        self.registry.get_service_instances(service_name).await
    }
}
