// Metrics collection for microservices
use prometheus::{Counter, Gauge, Histogram, Registry, Opts, HistogramOpts};
use std::sync::Arc;
use crate::error::ServiceResult;

pub struct ServiceMetrics {
    // Request metrics
    pub requests_total: Counter,
    pub requests_duration_seconds: Histogram,
    pub requests_in_flight: Gauge,
    
    // Error metrics
    pub errors_total: Counter,
    pub error_rate: Gauge,
    
    // Authentication metrics
    pub auth_events_total: Counter,
    pub auth_failures_total: Counter,
    
    // Conversion metrics
    pub conversion_jobs_started_total: Counter,
    pub conversion_jobs_completed_total: Counter,
    pub conversion_jobs_failed_total: Counter,
    pub conversion_duration_seconds: Histogram,
    pub conversion_queue_length: Gauge,
    
    // File metrics
    pub files_uploaded_total: Counter,
    pub file_upload_size_bytes: Histogram,
    pub files_downloaded_total: Counter,
    
    // Database metrics
    pub database_connections_active: Gauge,
    pub database_queries_total: Counter,
    pub database_query_duration_seconds: Histogram,
    
    // Redis metrics
    pub redis_commands_total: Counter,
    pub redis_command_duration_seconds: Histogram,
    pub redis_connections_active: Gauge,
    
    // Circuit breaker metrics
    pub circuit_breaker_state: Gauge,
    pub circuit_breaker_failures_total: Counter,
    
    // System metrics
    pub memory_usage_bytes: Gauge,
    pub cpu_usage_percent: Gauge,
}

impl ServiceMetrics {
    pub fn new(service_name: &str, registry: &Registry) -> ServiceResult<Self> {
        let requests_total = Counter::with_opts(
            Opts::new("requests_total", "Total number of HTTP requests")
                .namespace(service_name)
        )?;
        registry.register(Box::new(requests_total.clone()))?;

        let requests_duration_seconds = Histogram::with_opts(
            HistogramOpts::new("requests_duration_seconds", "HTTP request duration in seconds")
                .namespace(service_name)
                .buckets(vec![0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0])
        )?;
        registry.register(Box::new(requests_duration_seconds.clone()))?;

        let requests_in_flight = Gauge::with_opts(
            Opts::new("requests_in_flight", "Number of HTTP requests currently being processed")
                .namespace(service_name)
        )?;
        registry.register(Box::new(requests_in_flight.clone()))?;

        let errors_total = Counter::with_opts(
            Opts::new("errors_total", "Total number of errors")
                .namespace(service_name)
        )?;
        registry.register(Box::new(errors_total.clone()))?;

        let error_rate = Gauge::with_opts(
            Opts::new("error_rate", "Current error rate")
                .namespace(service_name)
        )?;
        registry.register(Box::new(error_rate.clone()))?;

        let auth_events_total = Counter::with_opts(
            Opts::new("auth_events_total", "Total number of authentication events")
                .namespace(service_name)
        )?;
        registry.register(Box::new(auth_events_total.clone()))?;

        let auth_failures_total = Counter::with_opts(
            Opts::new("auth_failures_total", "Total number of authentication failures")
                .namespace(service_name)
        )?;
        registry.register(Box::new(auth_failures_total.clone()))?;

        let conversion_jobs_started_total = Counter::with_opts(
            Opts::new("conversion_jobs_started_total", "Total number of conversion jobs started")
                .namespace(service_name)
        )?;
        registry.register(Box::new(conversion_jobs_started_total.clone()))?;

        let conversion_jobs_completed_total = Counter::with_opts(
            Opts::new("conversion_jobs_completed_total", "Total number of conversion jobs completed")
                .namespace(service_name)
        )?;
        registry.register(Box::new(conversion_jobs_completed_total.clone()))?;

        let conversion_jobs_failed_total = Counter::with_opts(
            Opts::new("conversion_jobs_failed_total", "Total number of conversion jobs failed")
                .namespace(service_name)
        )?;
        registry.register(Box::new(conversion_jobs_failed_total.clone()))?;

        let conversion_duration_seconds = Histogram::with_opts(
            HistogramOpts::new("conversion_duration_seconds", "Conversion job duration in seconds")
                .namespace(service_name)
                .buckets(vec![0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0])
        )?;
        registry.register(Box::new(conversion_duration_seconds.clone()))?;

        let conversion_queue_length = Gauge::with_opts(
            Opts::new("conversion_queue_length", "Number of jobs in conversion queue")
                .namespace(service_name)
        )?;
        registry.register(Box::new(conversion_queue_length.clone()))?;

        let files_uploaded_total = Counter::with_opts(
            Opts::new("files_uploaded_total", "Total number of files uploaded")
                .namespace(service_name)
        )?;
        registry.register(Box::new(files_uploaded_total.clone()))?;

        let file_upload_size_bytes = Histogram::with_opts(
            HistogramOpts::new("file_upload_size_bytes", "File upload size in bytes")
                .namespace(service_name)
                .buckets(vec![1024.0, 10240.0, 102400.0, 1048576.0, 10485760.0, 104857600.0])
        )?;
        registry.register(Box::new(file_upload_size_bytes.clone()))?;

        let files_downloaded_total = Counter::with_opts(
            Opts::new("files_downloaded_total", "Total number of files downloaded")
                .namespace(service_name)
        )?;
        registry.register(Box::new(files_downloaded_total.clone()))?;

        let database_connections_active = Gauge::with_opts(
            Opts::new("database_connections_active", "Number of active database connections")
                .namespace(service_name)
        )?;
        registry.register(Box::new(database_connections_active.clone()))?;

        let database_queries_total = Counter::with_opts(
            Opts::new("database_queries_total", "Total number of database queries")
                .namespace(service_name)
        )?;
        registry.register(Box::new(database_queries_total.clone()))?;

        let database_query_duration_seconds = Histogram::with_opts(
            HistogramOpts::new("database_query_duration_seconds", "Database query duration in seconds")
                .namespace(service_name)
                .buckets(vec![0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0])
        )?;
        registry.register(Box::new(database_query_duration_seconds.clone()))?;

        let redis_commands_total = Counter::with_opts(
            Opts::new("redis_commands_total", "Total number of Redis commands")
                .namespace(service_name)
        )?;
        registry.register(Box::new(redis_commands_total.clone()))?;

        let redis_command_duration_seconds = Histogram::with_opts(
            HistogramOpts::new("redis_command_duration_seconds", "Redis command duration in seconds")
                .namespace(service_name)
                .buckets(vec![0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5])
        )?;
        registry.register(Box::new(redis_command_duration_seconds.clone()))?;

        let redis_connections_active = Gauge::with_opts(
            Opts::new("redis_connections_active", "Number of active Redis connections")
                .namespace(service_name)
        )?;
        registry.register(Box::new(redis_connections_active.clone()))?;

        let circuit_breaker_state = Gauge::with_opts(
            Opts::new("circuit_breaker_state", "Circuit breaker state (0=closed, 1=open, 2=half-open)")
                .namespace(service_name)
        )?;
        registry.register(Box::new(circuit_breaker_state.clone()))?;

        let circuit_breaker_failures_total = Counter::with_opts(
            Opts::new("circuit_breaker_failures_total", "Total number of circuit breaker failures")
                .namespace(service_name)
        )?;
        registry.register(Box::new(circuit_breaker_failures_total.clone()))?;

        let memory_usage_bytes = Gauge::with_opts(
            Opts::new("memory_usage_bytes", "Current memory usage in bytes")
                .namespace(service_name)
        )?;
        registry.register(Box::new(memory_usage_bytes.clone()))?;

        let cpu_usage_percent = Gauge::with_opts(
            Opts::new("cpu_usage_percent", "Current CPU usage percentage")
                .namespace(service_name)
        )?;
        registry.register(Box::new(cpu_usage_percent.clone()))?;

        Ok(Self {
            requests_total,
            requests_duration_seconds,
            requests_in_flight,
            errors_total,
            error_rate,
            auth_events_total,
            auth_failures_total,
            conversion_jobs_started_total,
            conversion_jobs_completed_total,
            conversion_jobs_failed_total,
            conversion_duration_seconds,
            conversion_queue_length,
            files_uploaded_total,
            file_upload_size_bytes,
            files_downloaded_total,
            database_connections_active,
            database_queries_total,
            database_query_duration_seconds,
            redis_commands_total,
            redis_command_duration_seconds,
            redis_connections_active,
            circuit_breaker_state,
            circuit_breaker_failures_total,
            memory_usage_bytes,
            cpu_usage_percent,
        })
    }

    pub async fn update_queue_length(&self, redis_client: &redis::Client) -> ServiceResult<()> {
        let mut conn = redis_client.get_async_connection().await?;
        let queue_length: i64 = redis::cmd("LLEN")
            .arg("conversion_queue")
            .query_async(&mut conn)
            .await?;
        
        self.conversion_queue_length.set(queue_length as f64);
        Ok(())
    }

    pub fn record_request_start(&self) {
        self.requests_total.inc();
        self.requests_in_flight.inc();
    }

    pub fn record_request_end(&self, duration: std::time::Duration, success: bool) {
        self.requests_in_flight.dec();
        self.requests_duration_seconds.observe(duration.as_secs_f64());
        
        if !success {
            self.errors_total.inc();
        }
    }

    pub fn update_system_metrics(&self) {
        // Update memory usage (simplified - in production use a proper system metrics library)
        if let Ok(info) = sys_info::mem_info() {
            let used_memory = (info.total - info.free) * 1024; // Convert to bytes
            self.memory_usage_bytes.set(used_memory as f64);
        }

        // Update CPU usage (simplified)
        if let Ok(load) = sys_info::loadavg() {
            self.cpu_usage_percent.set(load.one * 100.0);
        }
    }

    // Auto-scaling specific metric updates
    pub fn update_database_pool_utilization(&self, pool: &sqlx::PgPool) {
        let pool_size = pool.size() as f64;
        let max_size = pool.options().get_max_connections() as f64;
        let utilization = if max_size > 0.0 { pool_size / max_size } else { 0.0 };
        self.database_connections_active.set(utilization);
    }

    pub fn record_auth_request(&self, success: bool) {
        self.auth_events_total.inc();
        if !success {
            self.auth_failures_total.inc();
        }
    }

    pub fn record_conversion_job_start(&self) {
        self.conversion_jobs_started_total.inc();
    }

    pub fn record_conversion_job_complete(&self, duration: std::time::Duration, success: bool) {
        if success {
            self.conversion_jobs_completed_total.inc();
        } else {
            self.conversion_jobs_failed_total.inc();
        }
        self.conversion_duration_seconds.observe(duration.as_secs_f64());
    }

    pub fn record_file_upload(&self, size_bytes: u64) {
        self.files_uploaded_total.inc();
        self.file_upload_size_bytes.observe(size_bytes as f64);
    }

    pub fn record_file_download(&self) {
        self.files_downloaded_total.inc();
    }

    pub fn update_circuit_breaker_state(&self, state: crate::circuit_breaker::CircuitState) {
        let state_value = match state {
            crate::circuit_breaker::CircuitState::Closed => 0.0,
            crate::circuit_breaker::CircuitState::Open => 1.0,
            crate::circuit_breaker::CircuitState::HalfOpen => 2.0,
        };
        self.circuit_breaker_state.set(state_value);
    }

    pub fn record_circuit_breaker_failure(&self) {
        self.circuit_breaker_failures_total.inc();
    }
}

// Auto-scaling metrics collector that runs in background
pub struct AutoScalingMetricsCollector {
    metrics: Arc<ServiceMetrics>,
    redis_client: redis::Client,
    db_pool: sqlx::PgPool,
    collection_interval: Duration,
}

impl AutoScalingMetricsCollector {
    pub fn new(
        metrics: Arc<ServiceMetrics>,
        redis_client: redis::Client,
        db_pool: sqlx::PgPool,
        collection_interval: Duration,
    ) -> Self {
        Self {
            metrics,
            redis_client,
            db_pool,
            collection_interval,
        }
    }

    pub async fn start_collection_loop(&self) {
        let mut interval = tokio::time::interval(self.collection_interval);

        loop {
            interval.tick().await;

            // Update queue lengths
            if let Err(e) = self.metrics.update_queue_length(&self.redis_client).await {
                tracing::warn!("Failed to update queue length metric: {}", e);
            }

            // Update database pool utilization
            self.metrics.update_database_pool_utilization(&self.db_pool);

            // Update system metrics
            self.metrics.update_system_metrics();
        }
    }
}
