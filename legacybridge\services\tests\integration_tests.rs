// Enterprise-grade integration tests for service-to-service communication
// Tests authentication, API gateway routing, error handling, and performance

use legacybridge_shared::{
    service_client::ServiceClient,
    auth::{Claims, UserInfo},
    types::User,
    ServiceResult,
};
use serde_json::json;
use std::time::Duration;
use tokio::time::timeout;
use uuid::Uuid;

// Test configuration for enterprise scenarios
const AUTH_SERVICE_URL: &str = "http://localhost:3001";
const CONVERSION_SERVICE_URL: &str = "http://localhost:3002";
const FILE_SERVICE_URL: &str = "http://localhost:3003";
const JOB_SERVICE_URL: &str = "http://localhost:3004";
const KONG_GATEWAY_URL: &str = "http://localhost:8000";

const ENTERPRISE_TIMEOUT: Duration = Duration::from_secs(30);
const PERFORMANCE_THRESHOLD_MS: u64 = 1000;

/// Test service-to-service authentication flow
#[tokio::test]
async fn test_authentication_flow() {
    let client = ServiceClient::new(AUTH_SERVICE_URL.to_string());
    
    // Test user registration
    let register_request = json!({
        "username": "integration_test_user",
        "email": "<EMAIL>",
        "password": "SecurePassword123!",
        "roles": ["user", "tester"]
    });
    
    let register_response = timeout(
        ENTERPRISE_TIMEOUT,
        client.post("/api/v1/auth/register", &register_request, None)
    ).await
    .expect("Registration should not timeout")
    .expect("Registration should succeed");
    
    assert_eq!(register_response.status(), 201);
    
    // Test user login
    let login_request = json!({
        "username": "integration_test_user",
        "password": "SecurePassword123!"
    });
    
    let login_response = timeout(
        ENTERPRISE_TIMEOUT,
        client.post("/api/v1/auth/login", &login_request, None)
    ).await
    .expect("Login should not timeout")
    .expect("Login should succeed");
    
    assert_eq!(login_response.status(), 200);
    
    let login_data: serde_json::Value = login_response.json().await
        .expect("Login response should be valid JSON");
    
    let token = login_data["token"].as_str()
        .expect("Login response should contain token");
    
    // Test token validation
    let validation_request = json!({
        "token": token
    });
    
    let validation_response = timeout(
        ENTERPRISE_TIMEOUT,
        client.post("/api/v1/auth/validate", &validation_request, None)
    ).await
    .expect("Validation should not timeout")
    .expect("Validation should succeed");
    
    assert_eq!(validation_response.status(), 200);
    
    let validation_data: serde_json::Value = validation_response.json().await
        .expect("Validation response should be valid JSON");
    
    assert_eq!(validation_data["valid"], true);
    assert_eq!(validation_data["user_id"].as_str().unwrap().len(), 36); // UUID length
}

/// Test API Gateway routing and authentication
#[tokio::test]
async fn test_api_gateway_routing() {
    let gateway_client = ServiceClient::new(KONG_GATEWAY_URL.to_string());
    
    // Test health endpoints (should be public)
    let health_endpoints = vec![
        "/auth/health",
        "/convert/health", 
        "/files/health",
        "/jobs/health"
    ];
    
    for endpoint in health_endpoints {
        let response = timeout(
            ENTERPRISE_TIMEOUT,
            gateway_client.get(endpoint, None)
        ).await
        .expect(&format!("Health check {} should not timeout", endpoint))
        .expect(&format!("Health check {} should succeed", endpoint));
        
        assert_eq!(response.status(), 200, "Health endpoint {} should return 200", endpoint);
        
        // Verify response headers
        assert!(response.headers().contains_key("x-service-name"));
        assert!(response.headers().contains_key("x-request-id"));
    }
}

/// Test protected endpoints require authentication
#[tokio::test]
async fn test_protected_endpoints_authentication() {
    let gateway_client = ServiceClient::new(KONG_GATEWAY_URL.to_string());
    
    // Test protected endpoints without authentication
    let protected_endpoints = vec![
        "/auth/api/v1/users",
        "/convert/api/v1/convert",
        "/files/api/v1/files",
        "/jobs/api/v1/jobs"
    ];
    
    for endpoint in protected_endpoints {
        let response = gateway_client.get(endpoint, None).await;
        
        match response {
            Ok(resp) => {
                assert_eq!(resp.status(), 401, 
                          "Protected endpoint {} should return 401 without auth", endpoint);
            }
            Err(_) => {
                // Some endpoints might not be implemented yet, that's ok for this test
                continue;
            }
        }
    }
}

/// Test service-to-service communication with authentication
#[tokio::test]
async fn test_authenticated_service_communication() {
    // First, get a valid token
    let auth_client = ServiceClient::new(AUTH_SERVICE_URL.to_string());
    let gateway_client = ServiceClient::new(KONG_GATEWAY_URL.to_string());
    
    let login_request = json!({
        "username": "integration_test_user",
        "password": "SecurePassword123!"
    });
    
    let login_response = auth_client.post("/api/v1/auth/login", &login_request, None).await
        .expect("Login should succeed");
    
    if login_response.status() != 200 {
        // User might not exist, try to register first
        let register_request = json!({
            "username": "integration_test_user",
            "email": "<EMAIL>", 
            "password": "SecurePassword123!",
            "roles": ["user", "tester"]
        });
        
        auth_client.post("/api/v1/auth/register", &register_request, None).await
            .expect("Registration should succeed");
        
        // Try login again
        let login_response = auth_client.post("/api/v1/auth/login", &login_request, None).await
            .expect("Login after registration should succeed");
        
        assert_eq!(login_response.status(), 200);
    }
    
    let login_data: serde_json::Value = login_response.json().await
        .expect("Login response should be valid JSON");
    
    let token = login_data["token"].as_str()
        .expect("Login response should contain token");
    
    // Test authenticated requests through gateway
    let auth_header = format!("Bearer {}", token);
    let headers = Some(vec![("Authorization".to_string(), auth_header)]);
    
    // Test user profile endpoint
    let profile_response = gateway_client.get("/auth/api/v1/profile", headers.clone()).await;
    
    match profile_response {
        Ok(resp) => {
            assert!(resp.status() == 200 || resp.status() == 404, 
                   "Profile endpoint should return 200 or 404, got {}", resp.status());
        }
        Err(e) => {
            // Service might not be fully implemented, log and continue
            println!("Profile endpoint test failed (expected for incomplete implementation): {}", e);
        }
    }
}

/// Test error handling and circuit breaker behavior
#[tokio::test]
async fn test_error_handling_and_resilience() {
    let gateway_client = ServiceClient::new(KONG_GATEWAY_URL.to_string());
    
    // Test invalid endpoints
    let invalid_response = gateway_client.get("/nonexistent/endpoint", None).await;
    
    match invalid_response {
        Ok(resp) => {
            assert_eq!(resp.status(), 404, "Invalid endpoint should return 404");
        }
        Err(_) => {
            // Connection error is also acceptable for non-existent endpoints
        }
    }
    
    // Test malformed requests
    let malformed_request = "invalid json";
    let malformed_response = gateway_client.post_raw("/auth/api/v1/login", malformed_request, None).await;
    
    match malformed_response {
        Ok(resp) => {
            assert!(resp.status() >= 400, "Malformed request should return error status");
        }
        Err(_) => {
            // Connection error is acceptable
        }
    }
}

/// Test rate limiting functionality
#[tokio::test]
async fn test_rate_limiting() {
    let gateway_client = ServiceClient::new(KONG_GATEWAY_URL.to_string());
    
    // Make multiple rapid requests to trigger rate limiting
    let mut responses = Vec::new();
    
    for i in 0..20 {
        let response = gateway_client.get("/auth/health", None).await;
        
        match response {
            Ok(resp) => {
                responses.push(resp.status());
                
                // Check for rate limiting headers
                if resp.headers().contains_key("x-ratelimit-limit-minute") {
                    println!("Rate limit headers detected on request {}", i);
                }
                
                // If we get rate limited, that's expected
                if resp.status() == 429 {
                    println!("Rate limiting triggered on request {}", i);
                    break;
                }
            }
            Err(e) => {
                println!("Request {} failed: {}", i, e);
                break;
            }
        }
        
        // Small delay between requests
        tokio::time::sleep(Duration::from_millis(100)).await;
    }
    
    // We should have gotten at least some successful responses
    let success_count = responses.iter().filter(|&&status| status == 200).count();
    assert!(success_count > 0, "Should have at least some successful requests");
}

/// Test performance requirements (enterprise SLA)
#[tokio::test]
async fn test_performance_requirements() {
    let gateway_client = ServiceClient::new(KONG_GATEWAY_URL.to_string());
    
    // Test response times for health endpoints
    let endpoints = vec!["/auth/health", "/convert/health", "/files/health", "/jobs/health"];
    
    for endpoint in endpoints {
        let start_time = std::time::Instant::now();
        
        let response = gateway_client.get(endpoint, None).await;
        
        let duration = start_time.elapsed();
        
        match response {
            Ok(resp) => {
                assert_eq!(resp.status(), 200, "Health endpoint {} should be healthy", endpoint);
                
                // Enterprise performance requirement: < 1 second response time
                assert!(duration.as_millis() < PERFORMANCE_THRESHOLD_MS as u128,
                       "Endpoint {} took too long: {:?} (threshold: {}ms)", 
                       endpoint, duration, PERFORMANCE_THRESHOLD_MS);
                
                println!("Endpoint {} response time: {:?}", endpoint, duration);
            }
            Err(e) => {
                println!("Endpoint {} failed: {}", endpoint, e);
                // For integration tests, we'll be lenient about service availability
            }
        }
    }
}

/// Test concurrent request handling
#[tokio::test]
async fn test_concurrent_request_handling() {
    let gateway_client = ServiceClient::new(KONG_GATEWAY_URL.to_string());
    
    // Launch multiple concurrent requests
    let concurrent_requests = 10;
    let mut handles = Vec::new();
    
    for i in 0..concurrent_requests {
        let client = gateway_client.clone();
        let handle = tokio::spawn(async move {
            let start_time = std::time::Instant::now();
            let response = client.get("/auth/health", None).await;
            let duration = start_time.elapsed();
            
            (i, response, duration)
        });
        handles.push(handle);
    }
    
    // Wait for all requests to complete
    let results = futures::future::join_all(handles).await;
    
    let mut success_count = 0;
    let mut total_duration = Duration::from_millis(0);
    
    for result in results {
        match result {
            Ok((i, response_result, duration)) => {
                match response_result {
                    Ok(resp) => {
                        if resp.status() == 200 {
                            success_count += 1;
                            total_duration += duration;
                        }
                        println!("Concurrent request {}: status={}, duration={:?}", 
                                i, resp.status(), duration);
                    }
                    Err(e) => {
                        println!("Concurrent request {} failed: {}", i, e);
                    }
                }
            }
            Err(e) => {
                println!("Concurrent request task failed: {}", e);
            }
        }
    }
    
    // Enterprise requirement: Should handle concurrent requests successfully
    assert!(success_count >= concurrent_requests / 2, 
           "Should handle at least 50% of concurrent requests successfully, got {}/{}", 
           success_count, concurrent_requests);
    
    if success_count > 0 {
        let avg_duration = total_duration / success_count as u32;
        println!("Average response time for {} concurrent requests: {:?}", 
                success_count, avg_duration);
        
        // Average response time should still be reasonable under load
        assert!(avg_duration.as_millis() < (PERFORMANCE_THRESHOLD_MS * 2) as u128,
               "Average response time under load too high: {:?}", avg_duration);
    }
}

/// Test CORS headers and security
#[tokio::test]
async fn test_security_headers() {
    let gateway_client = ServiceClient::new(KONG_GATEWAY_URL.to_string());
    
    // Test CORS preflight request
    let cors_response = gateway_client.options("/auth/health", Some(vec![
        ("Origin".to_string(), "https://example.com".to_string()),
        ("Access-Control-Request-Method".to_string(), "GET".to_string()),
    ])).await;
    
    match cors_response {
        Ok(resp) => {
            // Check for security headers
            let headers = resp.headers();
            
            // These headers should be present for security
            let security_headers = vec![
                "x-content-type-options",
                "x-frame-options", 
                "x-xss-protection"
            ];
            
            for header in security_headers {
                if headers.contains_key(header) {
                    println!("Security header found: {}", header);
                }
            }
            
            // CORS headers should be present
            if headers.contains_key("access-control-allow-origin") {
                println!("CORS headers configured");
            }
        }
        Err(e) => {
            println!("CORS test failed (may not be implemented): {}", e);
        }
    }
}
