#!/bin/bash

# Deploy Auto-Scaling Configuration for LegacyBridge Microservices
# Deploys HPA, VPA, monitoring, and alerting for all services

set -e

# Configuration
NAMESPACE="legacybridge"
MONITORING_NAMESPACE="monitoring"
KUBECTL_TIMEOUT="300s"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "🚀 Deploying Auto-Scaling Configuration for LegacyBridge"
echo "========================================================"

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}❌ kubectl is not installed or not in PATH${NC}"
        exit 1
    fi
    
    echo -e "${<PERSON><PERSON><PERSON>}✅ kubectl is available${NC}"
}

# Function to check if namespace exists
check_namespace() {
    local namespace=$1
    
    if kubectl get namespace "$namespace" &> /dev/null; then
        echo -e "${GREEN}✅ Namespace $namespace exists${NC}"
    else
        echo -e "${YELLOW}⚠️  Creating namespace $namespace${NC}"
        kubectl create namespace "$namespace"
    fi
}

# Function to apply Kubernetes manifests
apply_manifest() {
    local manifest_file=$1
    local description=$2
    
    echo -e "${BLUE}📄 Applying $description...${NC}"
    
    if kubectl apply -f "$manifest_file" --timeout="$KUBECTL_TIMEOUT"; then
        echo -e "${GREEN}✅ Successfully applied $description${NC}"
    else
        echo -e "${RED}❌ Failed to apply $description${NC}"
        return 1
    fi
}

# Function to wait for deployment to be ready
wait_for_deployment() {
    local deployment_name=$1
    local namespace=$2
    local timeout=${3:-300}
    
    echo -e "${BLUE}⏳ Waiting for deployment $deployment_name to be ready...${NC}"
    
    if kubectl wait --for=condition=available deployment/"$deployment_name" \
        --namespace="$namespace" --timeout="${timeout}s"; then
        echo -e "${GREEN}✅ Deployment $deployment_name is ready${NC}"
    else
        echo -e "${RED}❌ Deployment $deployment_name failed to become ready${NC}"
        return 1
    fi
}

# Function to check HPA status
check_hpa_status() {
    local hpa_name=$1
    local namespace=$2
    
    echo -e "${BLUE}📊 Checking HPA status for $hpa_name...${NC}"
    
    if kubectl get hpa "$hpa_name" --namespace="$namespace" &> /dev/null; then
        local current_replicas=$(kubectl get hpa "$hpa_name" --namespace="$namespace" -o jsonpath='{.status.currentReplicas}')
        local desired_replicas=$(kubectl get hpa "$hpa_name" --namespace="$namespace" -o jsonpath='{.status.desiredReplicas}')
        local min_replicas=$(kubectl get hpa "$hpa_name" --namespace="$namespace" -o jsonpath='{.spec.minReplicas}')
        local max_replicas=$(kubectl get hpa "$hpa_name" --namespace="$namespace" -o jsonpath='{.spec.maxReplicas}')
        
        echo -e "${GREEN}✅ HPA $hpa_name: Current=$current_replicas, Desired=$desired_replicas, Range=[$min_replicas-$max_replicas]${NC}"
    else
        echo -e "${RED}❌ HPA $hpa_name not found${NC}"
        return 1
    fi
}

# Function to check VPA status
check_vpa_status() {
    local vpa_name=$1
    local namespace=$2
    
    echo -e "${BLUE}📊 Checking VPA status for $vpa_name...${NC}"
    
    if kubectl get vpa "$vpa_name" --namespace="$namespace" &> /dev/null; then
        echo -e "${GREEN}✅ VPA $vpa_name is configured${NC}"
    else
        echo -e "${YELLOW}⚠️  VPA $vpa_name not found (VPA may not be installed)${NC}"
    fi
}

# Function to verify metrics are being collected
verify_metrics() {
    local service_name=$1
    local namespace=$2
    
    echo -e "${BLUE}📈 Verifying metrics for $service_name...${NC}"
    
    # Check if ServiceMonitor exists
    if kubectl get servicemonitor "${service_name}-metrics" --namespace="$namespace" &> /dev/null; then
        echo -e "${GREEN}✅ ServiceMonitor for $service_name is configured${NC}"
    else
        echo -e "${YELLOW}⚠️  ServiceMonitor for $service_name not found${NC}"
    fi
    
    # Check if PrometheusRule exists
    if kubectl get prometheusrule "${service_name}-alerts" --namespace="$namespace" &> /dev/null; then
        echo -e "${GREEN}✅ PrometheusRule for $service_name is configured${NC}"
    else
        echo -e "${YELLOW}⚠️  PrometheusRule for $service_name not found${NC}"
    fi
}

# Main deployment function
main() {
    echo "🔍 Pre-deployment checks..."
    
    # Check prerequisites
    check_kubectl
    check_namespace "$NAMESPACE"
    check_namespace "$MONITORING_NAMESPACE"
    
    echo ""
    echo "📦 Deploying auto-scaling configurations..."
    
    # Deploy auto-scaling configurations for each service
    local services=("auth-service" "conversion-service" "file-service" "job-service")
    
    for service in "${services[@]}"; do
        echo ""
        echo -e "${BLUE}🔧 Deploying auto-scaling for $service...${NC}"
        
        local manifest_file="../k8s/${service}-autoscaling.yaml"
        
        if [ -f "$manifest_file" ]; then
            apply_manifest "$manifest_file" "$service auto-scaling configuration"
            
            # Wait a moment for resources to be created
            sleep 5
            
            # Check HPA status
            check_hpa_status "${service}-hpa" "$NAMESPACE"
            
            # Check VPA status
            check_vpa_status "${service}-vpa" "$NAMESPACE"
            
            # Verify metrics configuration
            verify_metrics "$service" "$NAMESPACE"
            
        else
            echo -e "${RED}❌ Manifest file $manifest_file not found${NC}"
        fi
    done
    
    echo ""
    echo "🔍 Post-deployment verification..."
    
    # List all HPAs
    echo -e "${BLUE}📊 Current HPA status:${NC}"
    kubectl get hpa --namespace="$NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}📊 Current VPA status:${NC}"
    kubectl get vpa --namespace="$NAMESPACE" -o wide 2>/dev/null || echo "VPA not available"
    
    echo ""
    echo -e "${BLUE}📊 Current Pod Disruption Budgets:${NC}"
    kubectl get pdb --namespace="$NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}📊 ServiceMonitors:${NC}"
    kubectl get servicemonitor --namespace="$NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}📊 PrometheusRules:${NC}"
    kubectl get prometheusrule --namespace="$NAMESPACE" -o wide
    
    echo ""
    echo "🧪 Testing auto-scaling functionality..."
    
    # Test HPA functionality by checking metrics
    for service in "${services[@]}"; do
        echo -e "${BLUE}🔍 Testing metrics for $service...${NC}"
        
        # Try to get metrics from the service
        local pod_name=$(kubectl get pods --namespace="$NAMESPACE" -l app="$service" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
        
        if [ -n "$pod_name" ]; then
            echo -e "${GREEN}✅ Found pod for $service: $pod_name${NC}"
            
            # Check if metrics endpoint is accessible
            if kubectl exec --namespace="$NAMESPACE" "$pod_name" -- curl -s http://localhost:9090/metrics > /dev/null 2>&1; then
                echo -e "${GREEN}✅ Metrics endpoint accessible for $service${NC}"
            else
                echo -e "${YELLOW}⚠️  Metrics endpoint not accessible for $service${NC}"
            fi
        else
            echo -e "${YELLOW}⚠️  No pods found for $service${NC}"
        fi
    done
    
    echo ""
    echo "📋 Auto-scaling deployment summary:"
    echo "=================================="
    
    # Count deployed resources
    local hpa_count=$(kubectl get hpa --namespace="$NAMESPACE" --no-headers | wc -l)
    local vpa_count=$(kubectl get vpa --namespace="$NAMESPACE" --no-headers 2>/dev/null | wc -l || echo "0")
    local pdb_count=$(kubectl get pdb --namespace="$NAMESPACE" --no-headers | wc -l)
    local sm_count=$(kubectl get servicemonitor --namespace="$NAMESPACE" --no-headers | wc -l)
    local pr_count=$(kubectl get prometheusrule --namespace="$NAMESPACE" --no-headers | wc -l)
    
    echo -e "${GREEN}✅ HPAs deployed: $hpa_count${NC}"
    echo -e "${GREEN}✅ VPAs deployed: $vpa_count${NC}"
    echo -e "${GREEN}✅ PDBs deployed: $pdb_count${NC}"
    echo -e "${GREEN}✅ ServiceMonitors deployed: $sm_count${NC}"
    echo -e "${GREEN}✅ PrometheusRules deployed: $pr_count${NC}"
    
    echo ""
    echo "🎯 Next steps:"
    echo "=============="
    echo "1. Monitor HPA behavior: kubectl get hpa -n $NAMESPACE -w"
    echo "2. Check metrics in Prometheus: kubectl port-forward -n $MONITORING_NAMESPACE svc/prometheus 9090:9090"
    echo "3. View Grafana dashboards: kubectl port-forward -n $MONITORING_NAMESPACE svc/grafana 3000:3000"
    echo "4. Test scaling by generating load on the services"
    echo "5. Monitor alerts in AlertManager"
    
    echo ""
    echo -e "${GREEN}🎉 Auto-scaling deployment completed successfully!${NC}"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        echo "📊 Auto-scaling status:"
        kubectl get hpa,vpa,pdb,servicemonitor,prometheusrule --namespace="$NAMESPACE"
        ;;
    "clean")
        echo "🧹 Cleaning up auto-scaling resources..."
        kubectl delete hpa,vpa,pdb,servicemonitor,prometheusrule --namespace="$NAMESPACE" --all
        echo "✅ Cleanup completed"
        ;;
    "help")
        echo "Usage: $0 [deploy|status|clean|help]"
        echo "  deploy: Deploy auto-scaling configuration (default)"
        echo "  status: Show current auto-scaling status"
        echo "  clean:  Remove all auto-scaling resources"
        echo "  help:   Show this help message"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
