// Circuit Breaker Monitoring and Alerting System
use crate::circuit_breaker::{CircuitBreakerRegistry, CircuitState, CircuitBreakerStats};
use crate::metrics::ServiceMetrics;
use crate::cache::CacheManager;
use crate::error::{ServiceError, ServiceResult};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::collections::HashMap;
use tokio::time::interval;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerAlert {
    pub service_name: String,
    pub state: CircuitState,
    pub failure_count: u32,
    pub timestamp: u64,
    pub message: String,
    pub severity: AlertSeverity,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerHealthReport {
    pub timestamp: u64,
    pub total_services: usize,
    pub healthy_services: usize,
    pub degraded_services: usize,
    pub failed_services: usize,
    pub service_details: HashMap<String, ServiceHealthDetail>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealthDetail {
    pub service_name: String,
    pub state: CircuitState,
    pub failure_count: u32,
    pub success_rate: f64,
    pub last_failure_time: Option<u64>,
    pub last_success_time: Option<u64>,
    pub uptime_percentage: f64,
}

pub struct CircuitBreakerMonitor {
    registry: Arc<CircuitBreakerRegistry>,
    metrics: Arc<ServiceMetrics>,
    cache: Arc<CacheManager>,
    alert_thresholds: AlertThresholds,
    monitoring_interval: Duration,
    alert_history: Arc<tokio::sync::Mutex<Vec<CircuitBreakerAlert>>>,
}

#[derive(Debug, Clone)]
pub struct AlertThresholds {
    pub failure_count_warning: u32,
    pub failure_count_critical: u32,
    pub success_rate_warning: f64,
    pub success_rate_critical: f64,
    pub downtime_warning_minutes: u64,
    pub downtime_critical_minutes: u64,
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            failure_count_warning: 5,
            failure_count_critical: 10,
            success_rate_warning: 0.9,
            success_rate_critical: 0.8,
            downtime_warning_minutes: 5,
            downtime_critical_minutes: 15,
        }
    }
}

impl CircuitBreakerMonitor {
    pub fn new(
        registry: Arc<CircuitBreakerRegistry>,
        metrics: Arc<ServiceMetrics>,
        cache: Arc<CacheManager>,
        monitoring_interval: Duration,
    ) -> Self {
        Self {
            registry,
            metrics,
            cache,
            alert_thresholds: AlertThresholds::default(),
            monitoring_interval,
            alert_history: Arc::new(tokio::sync::Mutex::new(Vec::new())),
        }
    }

    pub fn with_alert_thresholds(mut self, thresholds: AlertThresholds) -> Self {
        self.alert_thresholds = thresholds;
        self
    }

    /// Start the monitoring loop
    pub async fn start_monitoring(&self) -> ServiceResult<()> {
        let registry = self.registry.clone();
        let metrics = self.metrics.clone();
        let cache = self.cache.clone();
        let thresholds = self.alert_thresholds.clone();
        let alert_history = self.alert_history.clone();
        let interval_duration = self.monitoring_interval;

        tokio::spawn(async move {
            let mut interval = interval(interval_duration);
            
            loop {
                interval.tick().await;
                
                if let Err(e) = Self::monitor_circuit_breakers(
                    &registry,
                    &metrics,
                    &cache,
                    &thresholds,
                    &alert_history,
                ).await {
                    tracing::error!("Circuit breaker monitoring error: {}", e);
                }
            }
        });

        Ok(())
    }

    async fn monitor_circuit_breakers(
        registry: &CircuitBreakerRegistry,
        metrics: &ServiceMetrics,
        cache: &CacheManager,
        thresholds: &AlertThresholds,
        alert_history: &Arc<tokio::sync::Mutex<Vec<CircuitBreakerAlert>>>,
    ) -> ServiceResult<()> {
        let stats = registry.get_all_stats();
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let mut alerts = Vec::new();
        let mut health_details = HashMap::new();

        for (service_name, circuit_stats) in stats {
            let health_detail = Self::analyze_service_health(&service_name, &circuit_stats, thresholds, now);
            
            // Check for alerts
            if let Some(alert) = Self::check_for_alerts(&service_name, &circuit_stats, thresholds, now) {
                alerts.push(alert);
            }

            // Update metrics
            Self::update_circuit_breaker_metrics(metrics, &service_name, &circuit_stats);

            health_details.insert(service_name, health_detail);
        }

        // Store alerts
        if !alerts.is_empty() {
            let mut alert_history_guard = alert_history.lock().await;
            alert_history_guard.extend(alerts.clone());
            
            // Keep only last 1000 alerts
            if alert_history_guard.len() > 1000 {
                alert_history_guard.drain(0..alert_history_guard.len() - 1000);
            }
        }

        // Generate health report
        let health_report = Self::generate_health_report(&health_details, now);
        
        // Cache the health report
        let _ = cache.set_with_ttl(
            "circuit_breaker:health_report",
            &health_report,
            Duration::from_secs(60),
        ).await;

        // Log alerts
        for alert in alerts {
            match alert.severity {
                AlertSeverity::Info => tracing::info!("Circuit Breaker Alert: {}", alert.message),
                AlertSeverity::Warning => tracing::warn!("Circuit Breaker Alert: {}", alert.message),
                AlertSeverity::Critical => tracing::error!("Circuit Breaker Alert: {}", alert.message),
            }
        }

        Ok(())
    }

    fn analyze_service_health(
        service_name: &str,
        stats: &CircuitBreakerStats,
        _thresholds: &AlertThresholds,
        now: u64,
    ) -> ServiceHealthDetail {
        let last_failure_time = stats.last_failure_time.map(|t| {
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs()
                .saturating_sub(t.elapsed().as_secs())
        });

        let last_success_time = stats.last_success_time.map(|t| {
            SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs()
                .saturating_sub(t.elapsed().as_secs())
        });

        // Calculate success rate (simplified)
        let success_rate = match stats.state {
            CircuitState::Closed => 1.0,
            CircuitState::HalfOpen => 0.5,
            CircuitState::Open => 0.0,
        };

        // Calculate uptime percentage (simplified)
        let uptime_percentage = match stats.state {
            CircuitState::Closed => 100.0,
            CircuitState::HalfOpen => 50.0,
            CircuitState::Open => 0.0,
        };

        ServiceHealthDetail {
            service_name: service_name.to_string(),
            state: stats.state,
            failure_count: stats.failure_count,
            success_rate,
            last_failure_time,
            last_success_time,
            uptime_percentage,
        }
    }

    fn check_for_alerts(
        service_name: &str,
        stats: &CircuitBreakerStats,
        thresholds: &AlertThresholds,
        now: u64,
    ) -> Option<CircuitBreakerAlert> {
        // Check for circuit breaker state changes
        match stats.state {
            CircuitState::Open => {
                Some(CircuitBreakerAlert {
                    service_name: service_name.to_string(),
                    state: stats.state,
                    failure_count: stats.failure_count,
                    timestamp: now,
                    message: format!(
                        "Circuit breaker OPEN for service '{}' - {} consecutive failures",
                        service_name, stats.failure_count
                    ),
                    severity: AlertSeverity::Critical,
                })
            }
            CircuitState::HalfOpen => {
                Some(CircuitBreakerAlert {
                    service_name: service_name.to_string(),
                    state: stats.state,
                    failure_count: stats.failure_count,
                    timestamp: now,
                    message: format!(
                        "Circuit breaker HALF-OPEN for service '{}' - attempting recovery",
                        service_name
                    ),
                    severity: AlertSeverity::Warning,
                })
            }
            CircuitState::Closed => {
                // Check for high failure count even when closed
                if stats.failure_count >= thresholds.failure_count_warning {
                    Some(CircuitBreakerAlert {
                        service_name: service_name.to_string(),
                        state: stats.state,
                        failure_count: stats.failure_count,
                        timestamp: now,
                        message: format!(
                            "High failure count for service '{}' - {} failures (threshold: {})",
                            service_name, stats.failure_count, thresholds.failure_count_warning
                        ),
                        severity: if stats.failure_count >= thresholds.failure_count_critical {
                            AlertSeverity::Critical
                        } else {
                            AlertSeverity::Warning
                        },
                    })
                } else {
                    None
                }
            }
        }
    }

    fn update_circuit_breaker_metrics(
        metrics: &ServiceMetrics,
        service_name: &str,
        stats: &CircuitBreakerStats,
    ) {
        // Update circuit breaker state metric
        metrics.update_circuit_breaker_state(stats.state);

        // Update failure count metric
        // Note: This would need to be implemented in the metrics module
        // metrics.set_circuit_breaker_failure_count(service_name, stats.failure_count);
    }

    fn generate_health_report(
        health_details: &HashMap<String, ServiceHealthDetail>,
        timestamp: u64,
    ) -> CircuitBreakerHealthReport {
        let total_services = health_details.len();
        let mut healthy_services = 0;
        let mut degraded_services = 0;
        let mut failed_services = 0;

        for detail in health_details.values() {
            match detail.state {
                CircuitState::Closed => healthy_services += 1,
                CircuitState::HalfOpen => degraded_services += 1,
                CircuitState::Open => failed_services += 1,
            }
        }

        CircuitBreakerHealthReport {
            timestamp,
            total_services,
            healthy_services,
            degraded_services,
            failed_services,
            service_details: health_details.clone(),
        }
    }

    /// Get current health report
    pub async fn get_health_report(&self) -> ServiceResult<CircuitBreakerHealthReport> {
        if let Ok(Some(report)) = self.cache.get::<CircuitBreakerHealthReport>("circuit_breaker:health_report").await {
            Ok(report)
        } else {
            // Generate fresh report
            let stats = self.registry.get_all_stats();
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs();

            let mut health_details = HashMap::new();
            for (service_name, circuit_stats) in stats {
                let health_detail = Self::analyze_service_health(&service_name, &circuit_stats, &self.alert_thresholds, now);
                health_details.insert(service_name, health_detail);
            }

            Ok(Self::generate_health_report(&health_details, now))
        }
    }

    /// Get recent alerts
    pub async fn get_recent_alerts(&self, limit: usize) -> Vec<CircuitBreakerAlert> {
        let alert_history = self.alert_history.lock().await;
        alert_history
            .iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    /// Force all circuit breakers open (for testing)
    pub fn force_all_open(&self) {
        self.registry.force_open_all();
    }

    /// Force all circuit breakers closed (for recovery)
    pub fn force_all_closed(&self) {
        self.registry.force_close_all();
    }
}
