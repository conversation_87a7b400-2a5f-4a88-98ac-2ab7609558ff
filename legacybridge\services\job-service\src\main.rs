// Job Processing Service for LegacyBridge
// Handles workflow orchestration, job scheduling, and background processing

mod handlers;
mod middleware;
mod service;
mod scheduler;
mod workflow;

use axum::{
    extract::Extension,
    http::{HeaderValue, Method},
    routing::{get, post, put, delete},
    Router,
};
use legacybridge_shared::{
    config::ServiceConfig,
    database::DatabaseManager,
    cache::CacheManager,
    metrics::ServiceMetrics,
    events::EventPublisher,
    ServiceResult,
};
use prometheus::Registry;
use std::{sync::Arc, time::Duration};
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
    timeout::TimeoutLayer,
    request_id::{MakeRequestUuid, PropagateRequestIdLayer, SetRequestIdLayer},
};
use tracing::{info, error};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::service::JobService;
use crate::scheduler::JobScheduler;
use crate::workflow::WorkflowEngine;

// Application state
#[derive(Clone)]
pub struct AppState {
    pub db: Arc<DatabaseManager>,
    pub cache: Arc<CacheManager>,
    pub metrics: Arc<ServiceMetrics>,
    pub event_publisher: Arc<EventPublisher>,
    pub job_service: Arc<JobService>,
    pub scheduler: Arc<JobScheduler>,
    pub workflow_engine: Arc<WorkflowEngine>,
    pub config: Arc<ServiceConfig>,
}

#[tokio::main]
async fn main() -> ServiceResult<()> {
    // Load configuration
    dotenvy::dotenv().ok();
    let config = Arc::new(ServiceConfig::job_service_config());
    config.validate().map_err(|e| legacybridge_shared::ServiceError::Configuration(e))?;

    // Initialize tracing
    init_tracing(&config.service.name)?;

    info!(
        service = %config.service.name,
        version = %config.service.version,
        port = %config.service.port,
        "Starting job processing service"
    );

    // Initialize database
    let db = Arc::new(DatabaseManager::new(&config.database.url).await?);
    info!("Database connection established");

    // Initialize cache
    let cache = Arc::new(CacheManager::new(
        &config.redis.url,
        Duration::from_secs(3600),
    )?);
    info!("Redis cache connection established");

    // Initialize metrics
    let registry = Registry::new();
    let metrics = Arc::new(ServiceMetrics::new(&config.service.name, &registry)?);

    // Initialize event publisher
    let event_publisher = Arc::new(EventPublisher::new(&config.redis.url)?);

    // Initialize job scheduler
    let scheduler = Arc::new(JobScheduler::new(&config.redis.url).await?);
    info!("Job scheduler initialized");

    // Initialize workflow engine
    let workflow_engine = Arc::new(WorkflowEngine::new(
        db.clone(),
        cache.clone(),
        event_publisher.clone(),
    ));
    info!("Workflow engine initialized");

    // Initialize job service
    let job_service = Arc::new(JobService::new(
        db.clone(),
        cache.clone(),
        scheduler.clone(),
        workflow_engine.clone(),
        event_publisher.clone(),
        metrics.clone(),
    ));

    // Create application state
    let state = AppState {
        db,
        cache,
        metrics,
        event_publisher,
        job_service,
        scheduler,
        workflow_engine,
        config: config.clone(),
    };

    // Start background services
    start_background_services(state.clone()).await?;

    // Build the application router
    let app = create_router(state.clone()).await?;

    // Start metrics server
    let metrics_app = Router::new()
        .route("/metrics", get(|| async move {
            use prometheus::Encoder;
            let encoder = prometheus::TextEncoder::new();
            let metric_families = registry.gather();
            match encoder.encode_to_string(&metric_families) {
                Ok(output) => output,
                Err(e) => {
                    error!("Failed to encode metrics: {}", e);
                    String::new()
                }
            }
        }))
        .route("/health", get(handlers::health::health_check))
        .layer(Extension(state.clone()));

    // Start metrics server in background
    let metrics_port = config.metrics.port;
    tokio::spawn(async move {
        let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", metrics_port))
            .await
            .expect("Failed to bind metrics server");
        
        info!("Metrics server listening on port {}", metrics_port);
        
        axum::serve(listener, metrics_app)
            .await
            .expect("Metrics server failed");
    });

    // Start main application server
    let listener = tokio::net::TcpListener::bind(format!("{}:{}", config.service.host, config.service.port))
        .await
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Failed to bind server: {}", e)))?;

    info!(
        "Job processing service listening on {}:{}",
        config.service.host, config.service.port
    );

    axum::serve(listener, app)
        .await
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Server error: {}", e)))?;

    Ok(())
}

async fn start_background_services(state: AppState) -> ServiceResult<()> {
    // Start job processor
    let processor_state = state.clone();
    tokio::spawn(async move {
        info!("Starting job processor");
        if let Err(e) = processor_state.job_service.start_job_processor().await {
            error!(error = %e, "Job processor failed");
        }
    });

    // Start scheduler
    let scheduler_state = state.clone();
    tokio::spawn(async move {
        info!("Starting job scheduler");
        if let Err(e) = scheduler_state.scheduler.start().await {
            error!(error = %e, "Job scheduler failed");
        }
    });

    // Start workflow engine
    let workflow_state = state.clone();
    tokio::spawn(async move {
        info!("Starting workflow engine");
        if let Err(e) = workflow_state.workflow_engine.start().await {
            error!(error = %e, "Workflow engine failed");
        }
    });

    Ok(())
}

async fn create_router(state: AppState) -> ServiceResult<Router> {
    // CORS configuration
    let cors = CorsLayer::new()
        .allow_origin("*".parse::<HeaderValue>().unwrap())
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE, Method::OPTIONS])
        .allow_headers(tower_http::cors::Any);

    // Request ID layer
    let request_id_layer = ServiceBuilder::new()
        .layer(SetRequestIdLayer::x_request_id(MakeRequestUuid))
        .layer(PropagateRequestIdLayer::x_request_id());

    // Build the router
    let router = Router::new()
        // Job management routes
        .route("/api/v1/jobs", post(handlers::jobs::create_job))
        .route("/api/v1/jobs", get(handlers::jobs::list_jobs))
        .route("/api/v1/jobs/:job_id", get(handlers::jobs::get_job))
        .route("/api/v1/jobs/:job_id", put(handlers::jobs::update_job))
        .route("/api/v1/jobs/:job_id", delete(handlers::jobs::delete_job))
        .route("/api/v1/jobs/:job_id/cancel", post(handlers::jobs::cancel_job))
        .route("/api/v1/jobs/:job_id/retry", post(handlers::jobs::retry_job))
        
        // Workflow routes
        .route("/api/v1/workflows", post(handlers::workflows::create_workflow))
        .route("/api/v1/workflows", get(handlers::workflows::list_workflows))
        .route("/api/v1/workflows/:workflow_id", get(handlers::workflows::get_workflow))
        .route("/api/v1/workflows/:workflow_id", put(handlers::workflows::update_workflow))
        .route("/api/v1/workflows/:workflow_id", delete(handlers::workflows::delete_workflow))
        .route("/api/v1/workflows/:workflow_id/execute", post(handlers::workflows::execute_workflow))
        
        // Schedule routes
        .route("/api/v1/schedules", post(handlers::schedules::create_schedule))
        .route("/api/v1/schedules", get(handlers::schedules::list_schedules))
        .route("/api/v1/schedules/:schedule_id", get(handlers::schedules::get_schedule))
        .route("/api/v1/schedules/:schedule_id", put(handlers::schedules::update_schedule))
        .route("/api/v1/schedules/:schedule_id", delete(handlers::schedules::delete_schedule))
        .route("/api/v1/schedules/:schedule_id/enable", post(handlers::schedules::enable_schedule))
        .route("/api/v1/schedules/:schedule_id/disable", post(handlers::schedules::disable_schedule))
        
        // Queue management routes
        .route("/api/v1/queues", get(handlers::queues::list_queues))
        .route("/api/v1/queues/:queue_name", get(handlers::queues::get_queue_stats))
        .route("/api/v1/queues/:queue_name/purge", post(handlers::queues::purge_queue))
        
        // Health and status
        .route("/health", get(handlers::health::health_check))
        .route("/ready", get(handlers::health::readiness_check))
        .route("/status", get(handlers::health::status))
        
        // Add middleware
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(TimeoutLayer::new(Duration::from_secs(30)))
                .layer(cors)
                .layer(request_id_layer)
                .layer(middleware::metrics::MetricsMiddleware::new())
                .layer(Extension(state))
        );

    Ok(router)
}

fn init_tracing(service_name: &str) -> ServiceResult<()> {
    // Initialize Jaeger tracer
    let tracer = opentelemetry_jaeger::new_agent_pipeline()
        .with_service_name(service_name)
        .install_simple()
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Failed to initialize tracer: {}", e)))?;

    let opentelemetry = tracing_opentelemetry::layer().with_tracer(tracer);

    // Initialize tracing subscriber
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "job_service=debug,tower_http=debug,axum::rejection=trace".into()),
        )
        .with(tracing_subscriber::fmt::layer().json())
        .with(opentelemetry)
        .init();

    Ok(())
}
