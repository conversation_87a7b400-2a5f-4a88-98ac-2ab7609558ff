[package]
name = "legacybridge-shared"
version = "0.1.0"
edition = "2021"
description = "Shared libraries and types for LegacyBridge microservices"

[dependencies]
# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "timeout"] }
hyper = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }

# Redis
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }

# HTTP client
reqwest = { version = "0.11", features = ["json", "multipart"] }

# UUID and time
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Validation
validator = { version = "0.16", features = ["derive"] }

# JWT
jsonwebtoken = "9.0"

# Metrics
prometheus = "0.13"

# Configuration
config = "0.13"
dotenvy = "0.15"

# Security
argon2 = "0.5"
rand = "0.8"

# Circuit breaker
circuit-breaker = "0.1"

# Base64
base64 = "0.21"

# File handling
mime = "0.3"
mime_guess = "2.0"

# AWS S3
aws-config = "1.0"
aws-sdk-s3 = "1.0"

[dev-dependencies]
tokio-test = "0.4"
testcontainers = "0.15"
