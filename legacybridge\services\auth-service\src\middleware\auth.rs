// Authentication middleware for protecting routes
use axum::{
    extract::{Extension, Request},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use legacybridge_shared::{
    auth::{<PERSON><PERSON><PERSON>, JwtManager},
    ServiceError,
};

use crate::AppState;

/// Extract user claims from JW<PERSON> token in Authorization header
pub async fn extract_user_claims(
    Extension(state): Extension<AppState>,
    headers: HeaderMap,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Get Authorization header
    let auth_header = headers
        .get("authorization")
        .and_then(|header| header.to_str().ok())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // Extract token from Bearer header
    let token = JwtManager::extract_bearer_token(auth_header)
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // Validate token
    let claims = state
        .jwt_manager
        .validate_token(token)
        .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // Check if session exists in cache
    let session_key = format!("session:{}", claims.sub);
    let session_exists = state
        .cache
        .exists(&session_key)
        .await
        .unwrap_or(false);

    if !session_exists {
        return Err(StatusCode::UNAUTHORIZED);
    }

    // Add claims to request extensions
    request.extensions_mut().insert(claims);

    Ok(next.run(request).await)
}

/// Middleware to require specific roles
pub fn require_roles(required_roles: Vec<String>) -> impl Fn(Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone {
    move |request: Request, next: Next| {
        let required_roles = required_roles.clone();
        Box::pin(async move {
            // Get claims from request extensions (should be set by extract_user_claims)
            let claims = request
                .extensions()
                .get::<Claims>()
                .ok_or(StatusCode::UNAUTHORIZED)?;

            // Check if user has any of the required roles
            let has_required_role = required_roles.iter().any(|role| {
                claims.roles.contains(role) || claims.roles.contains(&"admin".to_string())
            });

            if !has_required_role {
                return Err(StatusCode::FORBIDDEN);
            }

            Ok(next.run(request).await)
        })
    }
}

/// Middleware to require admin role
pub async fn require_admin(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let claims = request
        .extensions()
        .get::<Claims>()
        .ok_or(StatusCode::UNAUTHORIZED)?;

    if !claims.roles.contains(&"admin".to_string()) {
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}
