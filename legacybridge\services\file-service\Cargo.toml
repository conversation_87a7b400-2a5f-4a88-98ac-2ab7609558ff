[package]
name = "file-service"
version = "0.1.0"
edition = "2021"
description = "File management service for LegacyBridge microservices architecture"

[dependencies]
# Shared library
legacybridge-shared = { path = "../shared" }

# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "timeout", "request-id", "fs"] }
hyper = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["io"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json", "migrate"] }

# Redis for caching
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }

# UUID and time
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-opentelemetry = "0.21"
opentelemetry = "0.20"
opentelemetry-jaeger = "0.19"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Validation
validator = { version = "0.16", features = ["derive"] }

# Configuration
config = "0.13"
dotenvy = "0.15"

# Metrics
prometheus = "0.13"

# HTTP client for service communication
reqwest = { version = "0.11", features = ["json", "multipart", "stream"] }

# File handling
mime = "0.3"
mime_guess = "2.0"
tempfile = "3.0"
futures-util = "0.3"

# AWS S3
aws-config = "1.0"
aws-sdk-s3 = "1.0"
aws-smithy-types = "1.0"

# Checksums
sha2 = "0.10"
md5 = "0.7"

# Base64 encoding/decoding
base64 = "0.21"

# Multipart form handling
multer = "3.0"

[dev-dependencies]
tokio-test = "0.4"
testcontainers = "0.15"
axum-test = "14.0"
