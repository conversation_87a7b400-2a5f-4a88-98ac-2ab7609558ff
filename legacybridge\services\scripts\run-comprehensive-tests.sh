#!/bin/bash

# Comprehensive Test Suite Runner for LegacyBridge
# Runs unit tests, integration tests, performance tests, and chaos engineering tests

set -e

# Configuration
TEST_RESULTS_DIR="test-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$TEST_RESULTS_DIR/test_report_$TIMESTAMP.html"
KONG_PROXY_URL="http://localhost:8000"
SERVICES_TO_TEST=("auth-service:3001" "conversion-service:3002" "file-service:3003" "job-service:3004")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

echo "🧪 LegacyBridge Comprehensive Test Suite"
echo "========================================"
echo "Timestamp: $(date)"
echo "Test Results Directory: $TEST_RESULTS_DIR"
echo ""

# Function to create test results directory
setup_test_environment() {
    echo -e "${BLUE}🔧 Setting up test environment...${NC}"
    
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Create HTML report header
    cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>LegacyBridge Test Report - $TIMESTAMP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { color: green; }
        .fail { color: red; }
        .skip { color: orange; }
        .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>LegacyBridge Comprehensive Test Report</h1>
        <p><strong>Generated:</strong> $(date)</p>
        <p><strong>Test Environment:</strong> $(uname -a)</p>
    </div>
EOF
    
    echo -e "${GREEN}✅ Test environment ready${NC}"
}

# Function to check if services are running
check_services() {
    echo -e "${BLUE}🔍 Checking service availability...${NC}"
    
    local all_services_ready=true
    
    for service_info in "${SERVICES_TO_TEST[@]}"; do
        local service_name=$(echo $service_info | cut -d: -f1)
        local port=$(echo $service_info | cut -d: -f2)
        local health_url="http://localhost:$port/health"
        
        echo -n "  Checking $service_name on port $port... "
        
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Ready${NC}"
        else
            echo -e "${RED}❌ Not available${NC}"
            all_services_ready=false
        fi
    done
    
    # Check Kong Gateway
    echo -n "  Checking Kong Gateway... "
    if curl -f -s "$KONG_PROXY_URL/auth/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Ready${NC}"
    else
        echo -e "${YELLOW}⚠️  Kong may not be configured${NC}"
    fi
    
    if [ "$all_services_ready" = false ]; then
        echo -e "${YELLOW}⚠️  Some services are not available. Some tests may fail.${NC}"
        echo "   You can start services with: ./start-services.sh"
        echo ""
    fi
}

# Function to run unit tests
run_unit_tests() {
    echo -e "${PURPLE}🔬 Running Unit Tests...${NC}"
    echo "<div class=\"test-section\"><h2>Unit Tests</h2>" >> "$REPORT_FILE"
    
    local unit_test_start=$(date +%s)
    local unit_tests_passed=0
    local unit_tests_failed=0
    
    # Run Rust unit tests for shared library
    echo "  Running shared library unit tests..."
    cd ../shared
    
    if cargo test --lib 2>&1 | tee "$TEST_RESULTS_DIR/unit_tests_shared.log"; then
        echo -e "    ${GREEN}✅ Shared library unit tests passed${NC}"
        unit_tests_passed=$((unit_tests_passed + 1))
        echo "<p class=\"pass\">✅ Shared library unit tests: PASSED</p>" >> "$REPORT_FILE"
    else
        echo -e "    ${RED}❌ Shared library unit tests failed${NC}"
        unit_tests_failed=$((unit_tests_failed + 1))
        echo "<p class=\"fail\">❌ Shared library unit tests: FAILED</p>" >> "$REPORT_FILE"
    fi
    
    # Run unit tests for each service
    for service_info in "${SERVICES_TO_TEST[@]}"; do
        local service_name=$(echo $service_info | cut -d: -f1)
        
        echo "  Running $service_name unit tests..."
        cd "../$service_name"
        
        if cargo test --lib 2>&1 | tee "$TEST_RESULTS_DIR/unit_tests_$service_name.log"; then
            echo -e "    ${GREEN}✅ $service_name unit tests passed${NC}"
            unit_tests_passed=$((unit_tests_passed + 1))
            echo "<p class=\"pass\">✅ $service_name unit tests: PASSED</p>" >> "$REPORT_FILE"
        else
            echo -e "    ${RED}❌ $service_name unit tests failed${NC}"
            unit_tests_failed=$((unit_tests_failed + 1))
            echo "<p class=\"fail\">❌ $service_name unit tests: FAILED</p>" >> "$REPORT_FILE"
        fi
    done
    
    cd - > /dev/null
    
    local unit_test_end=$(date +%s)
    local unit_test_duration=$((unit_test_end - unit_test_start))
    
    echo "<p><strong>Duration:</strong> ${unit_test_duration}s</p>" >> "$REPORT_FILE"
    echo "<p><strong>Summary:</strong> $unit_tests_passed passed, $unit_tests_failed failed</p>" >> "$REPORT_FILE"
    echo "</div>" >> "$REPORT_FILE"
    
    TOTAL_TESTS=$((TOTAL_TESTS + unit_tests_passed + unit_tests_failed))
    PASSED_TESTS=$((PASSED_TESTS + unit_tests_passed))
    FAILED_TESTS=$((FAILED_TESTS + unit_tests_failed))
    
    echo -e "${BLUE}📊 Unit Tests Summary: $unit_tests_passed passed, $unit_tests_failed failed${NC}"
    echo ""
}

# Function to run integration tests
run_integration_tests() {
    echo -e "${PURPLE}🔗 Running Integration Tests...${NC}"
    echo "<div class=\"test-section\"><h2>Integration Tests</h2>" >> "$REPORT_FILE"
    
    local integration_test_start=$(date +%s)
    
    echo "  Running service integration tests..."
    cd ../tests
    
    if cargo test integration_tests 2>&1 | tee "$TEST_RESULTS_DIR/integration_tests.log"; then
        echo -e "    ${GREEN}✅ Integration tests passed${NC}"
        echo "<p class=\"pass\">✅ Service integration tests: PASSED</p>" >> "$REPORT_FILE"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "    ${RED}❌ Integration tests failed${NC}"
        echo "<p class=\"fail\">❌ Service integration tests: FAILED</p>" >> "$REPORT_FILE"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    cd - > /dev/null
    
    local integration_test_end=$(date +%s)
    local integration_test_duration=$((integration_test_end - integration_test_start))
    
    echo "<p><strong>Duration:</strong> ${integration_test_duration}s</p>" >> "$REPORT_FILE"
    echo "</div>" >> "$REPORT_FILE"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}📊 Integration Tests Complete${NC}"
    echo ""
}

# Function to run performance tests
run_performance_tests() {
    echo -e "${PURPLE}🚀 Running Performance Tests...${NC}"
    echo "<div class=\"test-section\"><h2>Performance Tests</h2>" >> "$REPORT_FILE"
    
    local perf_test_start=$(date +%s)
    
    echo "  Running performance test suite..."
    cd ../tests
    
    if cargo test performance_tests --release 2>&1 | tee "$TEST_RESULTS_DIR/performance_tests.log"; then
        echo -e "    ${GREEN}✅ Performance tests passed${NC}"
        echo "<p class=\"pass\">✅ Performance tests: PASSED</p>" >> "$REPORT_FILE"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "    ${RED}❌ Performance tests failed${NC}"
        echo "<p class=\"fail\">❌ Performance tests: FAILED</p>" >> "$REPORT_FILE"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    cd - > /dev/null
    
    local perf_test_end=$(date +%s)
    local perf_test_duration=$((perf_test_end - perf_test_start))
    
    echo "<p><strong>Duration:</strong> ${perf_test_duration}s</p>" >> "$REPORT_FILE"
    echo "</div>" >> "$REPORT_FILE"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}📊 Performance Tests Complete${NC}"
    echo ""
}

# Function to run chaos engineering tests
run_chaos_tests() {
    echo -e "${PURPLE}🔥 Running Chaos Engineering Tests...${NC}"
    echo "<div class=\"test-section\"><h2>Chaos Engineering Tests</h2>" >> "$REPORT_FILE"
    
    local chaos_test_start=$(date +%s)
    
    echo "  Running chaos engineering test suite..."
    cd ../tests
    
    if cargo test chaos_tests --release 2>&1 | tee "$TEST_RESULTS_DIR/chaos_tests.log"; then
        echo -e "    ${GREEN}✅ Chaos engineering tests passed${NC}"
        echo "<p class=\"pass\">✅ Chaos engineering tests: PASSED</p>" >> "$REPORT_FILE"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "    ${RED}❌ Chaos engineering tests failed${NC}"
        echo "<p class=\"fail\">❌ Chaos engineering tests: FAILED</p>" >> "$REPORT_FILE"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    cd - > /dev/null
    
    local chaos_test_end=$(date +%s)
    local chaos_test_duration=$((chaos_test_end - chaos_test_start))
    
    echo "<p><strong>Duration:</strong> ${chaos_test_duration}s</p>" >> "$REPORT_FILE"
    echo "</div>" >> "$REPORT_FILE"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}📊 Chaos Engineering Tests Complete${NC}"
    echo ""
}

# Function to run circuit breaker tests
run_circuit_breaker_tests() {
    echo -e "${PURPLE}🔧 Running Circuit Breaker Tests...${NC}"
    echo "<div class=\"test-section\"><h2>Circuit Breaker Tests</h2>" >> "$REPORT_FILE"
    
    local cb_test_start=$(date +%s)
    
    echo "  Running circuit breaker integration tests..."
    
    if ./test-circuit-breakers.sh 2>&1 | tee "$TEST_RESULTS_DIR/circuit_breaker_tests.log"; then
        echo -e "    ${GREEN}✅ Circuit breaker tests passed${NC}"
        echo "<p class=\"pass\">✅ Circuit breaker tests: PASSED</p>" >> "$REPORT_FILE"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "    ${RED}❌ Circuit breaker tests failed${NC}"
        echo "<p class=\"fail\">❌ Circuit breaker tests: FAILED</p>" >> "$REPORT_FILE"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    local cb_test_end=$(date +%s)
    local cb_test_duration=$((cb_test_end - cb_test_start))
    
    echo "<p><strong>Duration:</strong> ${cb_test_duration}s</p>" >> "$REPORT_FILE"
    echo "</div>" >> "$REPORT_FILE"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}📊 Circuit Breaker Tests Complete${NC}"
    echo ""
}

# Function to run horizontal scaling tests
run_scaling_tests() {
    echo -e "${PURPLE}⚖️  Running Horizontal Scaling Tests...${NC}"
    echo "<div class=\"test-section\"><h2>Horizontal Scaling Tests</h2>" >> "$REPORT_FILE"
    
    local scaling_test_start=$(date +%s)
    
    echo "  Running horizontal scaling tests..."
    
    if ./test-horizontal-scaling.sh 2>&1 | tee "$TEST_RESULTS_DIR/scaling_tests.log"; then
        echo -e "    ${GREEN}✅ Horizontal scaling tests passed${NC}"
        echo "<p class=\"pass\">✅ Horizontal scaling tests: PASSED</p>" >> "$REPORT_FILE"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "    ${RED}❌ Horizontal scaling tests failed${NC}"
        echo "<p class=\"fail\">❌ Horizontal scaling tests: FAILED</p>" >> "$REPORT_FILE"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    local scaling_test_end=$(date +%s)
    local scaling_test_duration=$((scaling_test_end - scaling_test_start))
    
    echo "<p><strong>Duration:</strong> ${scaling_test_duration}s</p>" >> "$REPORT_FILE"
    echo "</div>" >> "$REPORT_FILE"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}📊 Horizontal Scaling Tests Complete${NC}"
    echo ""
}

# Function to generate final report
generate_final_report() {
    echo -e "${BLUE}📋 Generating final test report...${NC}"
    
    local success_rate=0
    if [ $TOTAL_TESTS -gt 0 ]; then
        success_rate=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l)
    fi
    
    # Add summary to HTML report
    cat >> "$REPORT_FILE" << EOF
    <div class="summary">
        <h2>Test Summary</h2>
        <p><strong>Total Tests:</strong> $TOTAL_TESTS</p>
        <p><strong>Passed:</strong> <span class="pass">$PASSED_TESTS</span></p>
        <p><strong>Failed:</strong> <span class="fail">$FAILED_TESTS</span></p>
        <p><strong>Skipped:</strong> <span class="skip">$SKIPPED_TESTS</span></p>
        <p><strong>Success Rate:</strong> ${success_rate}%</p>
        <p><strong>Test Duration:</strong> $(date)</p>
    </div>
</body>
</html>
EOF
    
    echo ""
    echo "📊 Final Test Results"
    echo "===================="
    echo -e "Total Tests: $TOTAL_TESTS"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    echo -e "${YELLOW}Skipped: $SKIPPED_TESTS${NC}"
    echo -e "Success Rate: ${success_rate}%"
    echo ""
    echo -e "${BLUE}📄 Detailed report: $REPORT_FILE${NC}"
    echo -e "${BLUE}📁 Test logs: $TEST_RESULTS_DIR/${NC}"
}

# Main execution function
main() {
    local test_start_time=$(date +%s)
    
    setup_test_environment
    check_services
    
    # Run test suites based on arguments or run all
    case "${1:-all}" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "chaos")
            run_chaos_tests
            ;;
        "circuit-breaker")
            run_circuit_breaker_tests
            ;;
        "scaling")
            run_scaling_tests
            ;;
        "all")
            run_unit_tests
            run_integration_tests
            run_circuit_breaker_tests
            run_scaling_tests
            run_performance_tests
            run_chaos_tests
            ;;
        *)
            echo "Unknown test suite: $1"
            echo "Usage: $0 [unit|integration|performance|chaos|circuit-breaker|scaling|all]"
            exit 1
            ;;
    esac
    
    generate_final_report
    
    local test_end_time=$(date +%s)
    local total_duration=$((test_end_time - test_start_time))
    
    echo ""
    echo -e "${BLUE}⏱️  Total test execution time: ${total_duration}s${NC}"
    
    # Exit with appropriate code
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}❌ Some tests failed. Check the report for details.${NC}"
        exit 1
    fi
}

# Handle script arguments
main "$@"
