// Document converter implementation (lightweight alternative to Pandoc)
use legacybridge_shared::{ServiceError, ServiceResult};
use serde_json::Value;
use std::collections::HashMap;
use tracing::{info, warn, error};

pub mod rtf;
pub mod markdown;
pub mod html;
pub mod text;

pub struct DocumentConverter;

impl DocumentConverter {
    pub fn new() -> Self {
        Self
    }

    /// Convert document from one format to another
    pub async fn convert(
        &self,
        content: &[u8],
        input_format: &str,
        output_format: &str,
        options: Option<&Value>,
    ) -> ServiceResult<ConversionResult> {
        let start_time = std::time::Instant::now();
        
        info!(
            input_format = input_format,
            output_format = output_format,
            content_size = content.len(),
            "Starting document conversion"
        );

        // Detect encoding and convert to UTF-8 string
        let text_content = self.decode_content(content, input_format)?;
        
        // Parse input format
        let parsed_content = self.parse_input(&text_content, input_format)?;
        
        // Convert to output format
        let output_content = self.generate_output(&parsed_content, output_format, options)?;
        
        let processing_time = start_time.elapsed();
        
        info!(
            input_format = input_format,
            output_format = output_format,
            processing_time_ms = processing_time.as_millis(),
            output_size = output_content.len(),
            "Document conversion completed"
        );

        Ok(ConversionResult {
            content: base64::encode(&output_content),
            metadata: self.generate_metadata(&text_content, &output_content, input_format, output_format),
            warnings: vec![], // TODO: Collect warnings during conversion
            processing_time_ms: processing_time.as_millis() as u64,
        })
    }

    /// Detect content encoding and convert to UTF-8
    fn decode_content(&self, content: &[u8], format: &str) -> ServiceResult<String> {
        // Try UTF-8 first
        if let Ok(text) = std::str::from_utf8(content) {
            return Ok(text.to_string());
        }

        // Try to detect encoding
        let (decoded, encoding, had_errors) = encoding_rs::UTF_8.decode(content);
        
        if had_errors {
            warn!(
                format = format,
                detected_encoding = encoding.name(),
                "Content had encoding errors during conversion"
            );
        }

        Ok(decoded.to_string())
    }

    /// Parse input content based on format
    fn parse_input(&self, content: &str, format: &str) -> ServiceResult<ParsedDocument> {
        match format.to_lowercase().as_str() {
            "rtf" => rtf::parse_rtf(content),
            "md" | "markdown" => markdown::parse_markdown(content),
            "html" | "htm" => html::parse_html(content),
            "txt" | "text" => text::parse_text(content),
            "doc" | "docx" => {
                // For now, treat as plain text
                // In a full implementation, you'd use a proper DOC/DOCX parser
                warn!("DOC/DOCX parsing not fully implemented, treating as plain text");
                text::parse_text(content)
            }
            _ => Err(ServiceError::BadRequest(format!("Unsupported input format: {}", format))),
        }
    }

    /// Generate output content based on format
    fn generate_output(
        &self,
        document: &ParsedDocument,
        format: &str,
        options: Option<&Value>,
    ) -> ServiceResult<Vec<u8>> {
        let output = match format.to_lowercase().as_str() {
            "md" | "markdown" => markdown::generate_markdown(document, options)?,
            "html" | "htm" => html::generate_html(document, options)?,
            "txt" | "text" => text::generate_text(document, options)?,
            "json" => self.generate_json(document)?,
            _ => return Err(ServiceError::BadRequest(format!("Unsupported output format: {}", format))),
        };

        Ok(output.into_bytes())
    }

    /// Generate JSON representation of document
    fn generate_json(&self, document: &ParsedDocument) -> ServiceResult<String> {
        let json_doc = serde_json::json!({
            "title": document.title,
            "content": document.content,
            "metadata": document.metadata,
            "structure": document.structure
        });

        serde_json::to_string_pretty(&json_doc)
            .map_err(|e| ServiceError::Internal(format!("Failed to serialize to JSON: {}", e)))
    }

    /// Generate conversion metadata
    fn generate_metadata(
        &self,
        input_content: &str,
        output_content: &[u8],
        input_format: &str,
        output_format: &str,
    ) -> HashMap<String, Value> {
        let mut metadata = HashMap::new();
        
        metadata.insert("input_format".to_string(), Value::String(input_format.to_string()));
        metadata.insert("output_format".to_string(), Value::String(output_format.to_string()));
        metadata.insert("input_size_bytes".to_string(), Value::Number(input_content.len().into()));
        metadata.insert("output_size_bytes".to_string(), Value::Number(output_content.len().into()));
        metadata.insert("input_char_count".to_string(), Value::Number(input_content.chars().count().into()));
        metadata.insert("input_line_count".to_string(), Value::Number(input_content.lines().count().into()));
        metadata.insert("conversion_timestamp".to_string(), Value::String(chrono::Utc::now().to_rfc3339()));
        
        // Calculate compression ratio
        if !input_content.is_empty() {
            let compression_ratio = output_content.len() as f64 / input_content.len() as f64;
            metadata.insert("compression_ratio".to_string(), Value::Number(
                serde_json::Number::from_f64(compression_ratio).unwrap_or_else(|| 1.0.into())
            ));
        }

        metadata
    }

    /// Get supported input formats
    pub fn get_supported_input_formats() -> Vec<&'static str> {
        vec!["rtf", "md", "markdown", "html", "htm", "txt", "text", "doc", "docx"]
    }

    /// Get supported output formats
    pub fn get_supported_output_formats() -> Vec<&'static str> {
        vec!["md", "markdown", "html", "htm", "txt", "text", "json"]
    }

    /// Detect document format from content
    pub fn detect_format(&self, content: &[u8]) -> ServiceResult<String> {
        let text_content = self.decode_content(content, "unknown")?;
        let trimmed = text_content.trim();

        // RTF detection
        if trimmed.starts_with("{\\rtf") {
            return Ok("rtf".to_string());
        }

        // HTML detection
        if trimmed.starts_with("<!DOCTYPE html") || 
           trimmed.starts_with("<html") ||
           trimmed.contains("<html>") {
            return Ok("html".to_string());
        }

        // Markdown detection (heuristic)
        if trimmed.contains("# ") || 
           trimmed.contains("## ") ||
           trimmed.contains("```") ||
           trimmed.contains("**") ||
           trimmed.contains("*") && trimmed.contains("*") {
            return Ok("markdown".to_string());
        }

        // Default to plain text
        Ok("text".to_string())
    }
}

/// Parsed document structure
#[derive(Debug, Clone)]
pub struct ParsedDocument {
    pub title: Option<String>,
    pub content: String,
    pub metadata: HashMap<String, Value>,
    pub structure: Vec<DocumentElement>,
}

/// Document elements
#[derive(Debug, Clone)]
pub enum DocumentElement {
    Heading { level: u8, text: String },
    Paragraph { text: String },
    List { items: Vec<String>, ordered: bool },
    Code { text: String, language: Option<String> },
    Quote { text: String },
    Table { headers: Vec<String>, rows: Vec<Vec<String>> },
    Image { alt: String, url: String },
    Link { text: String, url: String },
}

/// Conversion result
#[derive(Debug, Clone)]
pub struct ConversionResult {
    pub content: String, // Base64 encoded
    pub metadata: HashMap<String, Value>,
    pub warnings: Vec<String>,
    pub processing_time_ms: u64,
}
