// Enterprise-grade database connection pool manager
// Provides advanced connection pooling, monitoring, and health management

use crate::error::{ServiceError, ServiceResult};
use sqlx::{PgPool, postgres::PgPoolOptions};
use std::time::{Duration, Instant};
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error};

/// Enterprise connection pool configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolConfig {
    /// Database connection URL
    pub database_url: String,
    
    /// Minimum number of connections to maintain
    pub min_connections: u32,
    
    /// Maximum number of connections in the pool
    pub max_connections: u32,
    
    /// Timeout for acquiring a connection from the pool
    pub acquire_timeout: Duration,
    
    /// Maximum idle time for a connection before it's closed
    pub idle_timeout: Duration,
    
    /// Maximum lifetime of a connection before it's recycled
    pub max_lifetime: Duration,
    
    /// Test query to validate connections
    pub test_query: String,
    
    /// Enable connection pool monitoring
    pub enable_monitoring: bool,
    
    /// Monitoring interval for pool statistics
    pub monitoring_interval: Duration,
    
    /// Enable slow query logging
    pub enable_slow_query_logging: bool,
    
    /// Threshold for slow query logging (in milliseconds)
    pub slow_query_threshold_ms: u64,
}

impl Default for PoolConfig {
    fn default() -> Self {
        Self {
            database_url: "postgresql://postgres:password@localhost:5432/legacybridge".to_string(),
            min_connections: 5,
            max_connections: 20,
            acquire_timeout: Duration::from_secs(10),
            idle_timeout: Duration::from_secs(600),
            max_lifetime: Duration::from_secs(1800),
            test_query: "SELECT 1".to_string(),
            enable_monitoring: true,
            monitoring_interval: Duration::from_secs(30),
            enable_slow_query_logging: true,
            slow_query_threshold_ms: 1000,
        }
    }
}

/// Connection pool statistics for monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStatistics {
    pub total_connections: u32,
    pub active_connections: u32,
    pub idle_connections: u32,
    pub pending_requests: u32,
    pub total_acquired: u64,
    pub total_released: u64,
    pub total_timeouts: u64,
    pub average_acquire_time_ms: f64,
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// Enterprise database pool manager
pub struct PoolManager {
    pool: PgPool,
    config: PoolConfig,
    statistics: Arc<RwLock<PoolStatistics>>,
    monitoring_handle: Option<tokio::task::JoinHandle<()>>,
}

impl PoolManager {
    /// Create a new pool manager with enterprise configuration
    pub async fn new(config: PoolConfig) -> ServiceResult<Self> {
        info!("Initializing enterprise database pool with config: min={}, max={}", 
              config.min_connections, config.max_connections);

        // Create the connection pool with enterprise settings
        let pool = PgPoolOptions::new()
            .min_connections(config.min_connections)
            .max_connections(config.max_connections)
            .acquire_timeout(config.acquire_timeout)
            .idle_timeout(config.idle_timeout)
            .max_lifetime(config.max_lifetime)
            .test_before_acquire(true)
            .connect(&config.database_url)
            .await
            .map_err(|e| {
                error!("Failed to create database pool: {}", e);
                ServiceError::DatabaseError(e.to_string())
            })?;

        // Run database migrations
        info!("Running database migrations...");
        sqlx::migrate!("./migrations")
            .run(&pool)
            .await
            .map_err(|e| {
                error!("Failed to run migrations: {}", e);
                ServiceError::DatabaseError(e.to_string())
            })?;

        // Initialize statistics
        let statistics = Arc::new(RwLock::new(PoolStatistics {
            total_connections: config.max_connections,
            active_connections: 0,
            idle_connections: config.min_connections,
            pending_requests: 0,
            total_acquired: 0,
            total_released: 0,
            total_timeouts: 0,
            average_acquire_time_ms: 0.0,
            last_updated: chrono::Utc::now(),
        }));

        let mut manager = Self {
            pool,
            config,
            statistics,
            monitoring_handle: None,
        };

        // Start monitoring if enabled
        if manager.config.enable_monitoring {
            manager.start_monitoring().await;
        }

        info!("Database pool manager initialized successfully");
        Ok(manager)
    }

    /// Get the underlying connection pool
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// Get current pool statistics
    pub async fn get_statistics(&self) -> PoolStatistics {
        self.statistics.read().await.clone()
    }

    /// Perform a health check on the database connection
    pub async fn health_check(&self) -> ServiceResult<Duration> {
        let start = Instant::now();
        
        sqlx::query(&self.config.test_query)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| {
                error!("Database health check failed: {}", e);
                ServiceError::DatabaseError(e.to_string())
            })?;

        let duration = start.elapsed();
        
        if duration > Duration::from_millis(self.config.slow_query_threshold_ms) {
            warn!("Slow health check detected: {:?}", duration);
        }

        Ok(duration)
    }

    /// Execute a query with performance monitoring
    pub async fn execute_monitored<'q, A>(
        &self,
        query: sqlx::query::Query<'q, sqlx::Postgres, A>,
        operation_name: &str,
    ) -> ServiceResult<sqlx::postgres::PgQueryResult>
    where
        A: 'q + sqlx::IntoArguments<'q, sqlx::Postgres>,
    {
        let start = Instant::now();
        let acquire_start = Instant::now();
        
        // Acquire connection with monitoring
        let mut conn = self.pool.acquire().await
            .map_err(|e| {
                error!("Failed to acquire database connection: {}", e);
                self.record_timeout();
                ServiceError::DatabaseError(e.to_string())
            })?;

        let acquire_duration = acquire_start.elapsed();
        self.record_acquire_time(acquire_duration).await;

        // Execute query
        let result = query.execute(&mut *conn).await;
        let total_duration = start.elapsed();

        // Log slow queries if enabled
        if self.config.enable_slow_query_logging && 
           total_duration.as_millis() > self.config.slow_query_threshold_ms as u128 {
            warn!("Slow query detected: operation='{}', duration={:?}", 
                  operation_name, total_duration);
        }

        // Record performance metrics
        self.record_query_performance(operation_name, total_duration, result.is_ok()).await;

        result.map_err(|e| {
            error!("Database query failed: operation='{}', error='{}'", operation_name, e);
            ServiceError::DatabaseError(e.to_string())
        })
    }

    /// Start background monitoring of pool statistics
    async fn start_monitoring(&mut self) {
        let pool = self.pool.clone();
        let statistics = self.statistics.clone();
        let interval = self.config.monitoring_interval;

        let handle = tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                // Update pool statistics
                let mut stats = statistics.write().await;
                stats.total_connections = pool.size();
                stats.idle_connections = pool.num_idle();
                stats.active_connections = pool.size() - pool.num_idle();
                stats.last_updated = chrono::Utc::now();
                
                // Log statistics periodically
                if stats.last_updated.timestamp() % 300 == 0 { // Every 5 minutes
                    info!("Pool stats: total={}, active={}, idle={}, avg_acquire_time={:.2}ms",
                          stats.total_connections, stats.active_connections, 
                          stats.idle_connections, stats.average_acquire_time_ms);
                }
            }
        });

        self.monitoring_handle = Some(handle);
    }

    /// Record connection acquire time for statistics
    async fn record_acquire_time(&self, duration: Duration) {
        let mut stats = self.statistics.write().await;
        stats.total_acquired += 1;
        
        // Calculate rolling average
        let new_time_ms = duration.as_millis() as f64;
        stats.average_acquire_time_ms = 
            (stats.average_acquire_time_ms * (stats.total_acquired - 1) as f64 + new_time_ms) 
            / stats.total_acquired as f64;
    }

    /// Record a connection timeout for statistics
    fn record_timeout(&self) {
        tokio::spawn({
            let statistics = self.statistics.clone();
            async move {
                let mut stats = statistics.write().await;
                stats.total_timeouts += 1;
            }
        });
    }

    /// Record query performance metrics
    async fn record_query_performance(&self, operation: &str, duration: Duration, success: bool) {
        // In a real implementation, you'd send this to your metrics system
        // For now, we'll just log it
        if !success || duration.as_millis() > self.config.slow_query_threshold_ms as u128 {
            warn!("Query performance: operation='{}', duration={:?}, success={}", 
                  operation, duration, success);
        }
    }

    /// Gracefully shutdown the pool manager
    pub async fn shutdown(self) -> ServiceResult<()> {
        info!("Shutting down database pool manager...");

        // Stop monitoring
        if let Some(handle) = self.monitoring_handle {
            handle.abort();
        }

        // Close the pool
        self.pool.close().await;
        
        info!("Database pool manager shutdown complete");
        Ok(())
    }

    /// Get detailed pool information for debugging
    pub async fn get_pool_info(&self) -> PoolInfo {
        let stats = self.get_statistics().await;
        
        PoolInfo {
            config: self.config.clone(),
            statistics: stats,
            is_closed: self.pool.is_closed(),
            options: PoolOptionsInfo {
                min_connections: self.config.min_connections,
                max_connections: self.config.max_connections,
                acquire_timeout: self.config.acquire_timeout,
                idle_timeout: self.config.idle_timeout,
                max_lifetime: self.config.max_lifetime,
            },
        }
    }
}

/// Detailed pool information for debugging and monitoring
#[derive(Debug, Serialize, Deserialize)]
pub struct PoolInfo {
    pub config: PoolConfig,
    pub statistics: PoolStatistics,
    pub is_closed: bool,
    pub options: PoolOptionsInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PoolOptionsInfo {
    pub min_connections: u32,
    pub max_connections: u32,
    pub acquire_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
}

impl Drop for PoolManager {
    fn drop(&mut self) {
        if let Some(handle) = self.monitoring_handle.take() {
            handle.abort();
        }
    }
}
