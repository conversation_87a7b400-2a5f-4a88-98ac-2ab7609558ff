// Conversion service implementation
use crate::converter::{DocumentConverter, ConversionResult};
use crate::queue::{JobQueue, QueuedJob, JobResult};
use legacybridge_shared::{
    database::DatabaseManager,
    cache::CacheManager,
    metrics::ServiceMetrics,
    events::EventPublisher,
    types::{ConversionRequest, JobStatusResponse, JobStatus, DomainEvent},
    ServiceError, ServiceResult,
};
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use tracing::{info, warn, error};
use uuid::Uuid;

pub struct ConversionService {
    db: Arc<DatabaseManager>,
    cache: Arc<CacheManager>,
    job_queue: Arc<JobQueue>,
    event_publisher: Arc<EventPublisher>,
    metrics: Arc<ServiceMetrics>,
    converter: DocumentConverter,
}

impl ConversionService {
    pub fn new(
        db: Arc<DatabaseManager>,
        cache: Arc<CacheManager>,
        job_queue: Arc<JobQueue>,
        event_publisher: Arc<EventPublisher>,
        metrics: Arc<ServiceMetrics>,
    ) -> Self {
        Self {
            db,
            cache,
            job_queue,
            event_publisher,
            metrics,
            converter: DocumentConverter::new(),
        }
    }

    /// Queue a conversion job
    pub async fn queue_conversion_job(
        &self,
        job_id: &str,
        request: ConversionRequest,
        priority: i32,
    ) -> ServiceResult<()> {
        let job = QueuedJob {
            job_id: job_id.to_string(),
            request,
            priority,
            queued_at: chrono::Utc::now(),
            attempts: 0,
            max_attempts: 3,
        };

        self.job_queue.enqueue_job(job).await?;

        // Publish event
        let event = DomainEvent::ConversionStarted {
            job_id: job_id.to_string(),
            user_id: Uuid::new_v4(), // TODO: Get from request context
            input_format: job.request.input_format.clone(),
            output_format: job.request.output_format.clone(),
            timestamp: chrono::Utc::now(),
        };

        if let Err(e) = self.event_publisher.publish(event).await {
            warn!(error = %e, job_id = job_id, "Failed to publish conversion started event");
        }

        Ok(())
    }

    /// Get job status
    pub async fn get_job_status(&self, job_id: &str) -> ServiceResult<Option<JobStatusResponse>> {
        let status = self.job_queue.get_job_status(job_id).await?;
        
        if let Some(status_str) = status {
            let job_status = match status_str.as_str() {
                "queued" => JobStatus::Queued,
                "processing" => JobStatus::Processing,
                "completed" => JobStatus::Completed,
                "failed" => JobStatus::Failed,
                "cancelled" => JobStatus::Cancelled,
                _ => JobStatus::Failed,
            };

            // Get job details for additional info
            let job = self.job_queue.get_job(job_id).await?;
            
            let response = JobStatusResponse {
                job_id: job_id.to_string(),
                status: job_status,
                progress: None, // TODO: Implement progress tracking
                created_at: job.map(|j| j.queued_at).unwrap_or_else(chrono::Utc::now),
                completed_at: if matches!(job_status, JobStatus::Completed | JobStatus::Failed | JobStatus::Cancelled) {
                    Some(chrono::Utc::now()) // TODO: Store actual completion time
                } else {
                    None
                },
                error: None, // TODO: Get error from job result
            };

            Ok(Some(response))
        } else {
            Ok(None)
        }
    }

    /// Get conversion result
    pub async fn get_conversion_result(&self, job_id: &str) -> ServiceResult<Option<legacybridge_shared::types::ConversionResult>> {
        let job_result = self.job_queue.get_job_result(job_id).await?;
        
        if let Some(result) = job_result {
            if result.status == JobStatus::Completed {
                if let Some(content) = result.result {
                    let conversion_result = legacybridge_shared::types::ConversionResult {
                        content,
                        metadata: result.metadata.unwrap_or_default().as_object()
                            .unwrap_or(&serde_json::Map::new())
                            .iter()
                            .map(|(k, v)| (k.clone(), v.clone()))
                            .collect(),
                        warnings: vec![], // TODO: Store warnings in job result
                        processing_time_ms: result.processing_time_ms.unwrap_or(0),
                    };
                    return Ok(Some(conversion_result));
                }
            }
        }
        
        Ok(None)
    }

    /// Cancel a job
    pub async fn cancel_job(&self, job_id: &str) -> ServiceResult<bool> {
        self.job_queue.cancel_job(job_id).await
    }

    /// Start the job processor (runs in background)
    pub async fn start_job_processor(&self) -> ServiceResult<()> {
        info!("Starting conversion job processor");
        
        loop {
            match self.process_next_job().await {
                Ok(processed) => {
                    if !processed {
                        // No jobs available, wait a bit
                        sleep(Duration::from_secs(1)).await;
                    }
                }
                Err(e) => {
                    error!(error = %e, "Error processing job");
                    sleep(Duration::from_secs(5)).await;
                }
            }
        }
    }

    /// Process the next job in the queue
    async fn process_next_job(&self) -> ServiceResult<bool> {
        let job = self.job_queue.dequeue_job().await?;
        
        if let Some(job) = job {
            info!(job_id = %job.job_id, "Processing conversion job");
            
            let start_time = std::time::Instant::now();
            
            // Update metrics
            self.metrics.conversion_jobs_started_total.inc();
            
            // Perform the conversion
            let result = self.perform_conversion(&job).await;
            
            let processing_time = start_time.elapsed();
            
            // Create job result
            let job_result = match result {
                Ok(conversion_result) => {
                    self.metrics.conversion_jobs_completed_total.inc();
                    
                    JobResult {
                        job_id: job.job_id.clone(),
                        status: JobStatus::Completed,
                        result: Some(conversion_result.content),
                        error: None,
                        processing_time_ms: Some(processing_time.as_millis() as u64),
                        completed_at: chrono::Utc::now(),
                        metadata: Some(serde_json::to_value(conversion_result.metadata).unwrap_or_default()),
                    }
                }
                Err(e) => {
                    self.metrics.conversion_jobs_failed_total.inc();
                    
                    JobResult {
                        job_id: job.job_id.clone(),
                        status: JobStatus::Failed,
                        result: None,
                        error: Some(e.to_string()),
                        processing_time_ms: Some(processing_time.as_millis() as u64),
                        completed_at: chrono::Utc::now(),
                        metadata: None,
                    }
                }
            };
            
            // Store the result
            self.job_queue.complete_job(job_result.clone()).await?;
            
            // Publish completion event
            let event = DomainEvent::ConversionCompleted {
                job_id: job.job_id.clone(),
                user_id: Uuid::new_v4(), // TODO: Get from job context
                success: job_result.status == JobStatus::Completed,
                processing_time_ms: processing_time.as_millis() as u64,
                timestamp: chrono::Utc::now(),
            };
            
            if let Err(e) = self.event_publisher.publish(event).await {
                warn!(error = %e, job_id = %job.job_id, "Failed to publish conversion completed event");
            }
            
            // Update metrics
            self.metrics.conversion_duration_seconds.observe(processing_time.as_secs_f64());
            
            info!(
                job_id = %job.job_id,
                status = ?job_result.status,
                processing_time_ms = processing_time.as_millis(),
                "Conversion job completed"
            );
            
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Perform the actual document conversion
    async fn perform_conversion(&self, job: &QueuedJob) -> ServiceResult<ConversionResult> {
        // Decode the base64 content
        let content_bytes = base64::decode(&job.request.content)
            .map_err(|e| ServiceError::BadRequest(format!("Invalid base64 content: {}", e)))?;
        
        // Perform the conversion
        self.converter.convert(
            &content_bytes,
            &job.request.input_format,
            &job.request.output_format,
            job.request.options.as_ref(),
        ).await
    }

    /// Get queue statistics
    pub async fn get_queue_stats(&self) -> ServiceResult<QueueStats> {
        let queue_length = self.job_queue.get_queue_length().await?;
        
        Ok(QueueStats {
            queue_length: queue_length as u32,
            processing_jobs: 0, // TODO: Track processing jobs
            completed_jobs_today: 0, // TODO: Implement daily stats
            failed_jobs_today: 0, // TODO: Implement daily stats
        })
    }
}

#[derive(Debug, serde::Serialize)]
pub struct QueueStats {
    pub queue_length: u32,
    pub processing_jobs: u32,
    pub completed_jobs_today: u32,
    pub failed_jobs_today: u32,
}
