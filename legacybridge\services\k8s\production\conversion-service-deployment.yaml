# Production Conversion Service Deployment
apiVersion: v1
kind: ConfigMap
metadata:
  name: conversion-service-config
  namespace: legacybridge
  labels:
    app: conversion-service
    component: config
data:
  config.yaml: |
    server:
      host: "0.0.0.0"
      port: 3002
      workers: 6
      max_request_size: 104857600  # 100MB
    
    redis:
      host: redis
      port: 6379
      pool_size: 15
      connection_timeout: 5
      command_timeout: 30
      queue_name: "conversion_jobs"
    
    conversion:
      max_file_size: 104857600  # 100MB
      timeout: 300  # 5 minutes
      temp_dir: "/tmp/conversions"
      supported_formats:
        input: ["docx", "doc", "pdf", "txt", "rtf", "odt"]
        output: ["pdf", "docx", "txt", "html"]
      quality_settings:
        pdf_dpi: 300
        image_compression: 85
    
    metrics:
      enabled: true
      port: 9090
      path: "/metrics"
    
    logging:
      level: "info"
      format: "json"
      output: "stdout"
    
    circuit_breaker:
      failure_threshold: 3
      recovery_timeout: 60
      request_timeout: 300
      half_open_max_calls: 2
    
    service_discovery:
      enabled: true
      health_check_interval: 30
      registration_ttl: 60

---
apiVersion: v1
kind: Secret
metadata:
  name: conversion-service-secrets
  namespace: legacybridge
  labels:
    app: conversion-service
    component: secrets
type: Opaque
data:
  REDIS_PASSWORD: ""  # empty for no password
  CONVERSION_API_KEY: "Y29udmVyc2lvbi1hcGkta2V5LTEyMw=="  # conversion-api-key-123

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversion-service
  namespace: legacybridge
  labels:
    app: conversion-service
    version: v1.0.0
    component: microservice
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
  selector:
    matchLabels:
      app: conversion-service
  template:
    metadata:
      labels:
        app: conversion-service
        version: v1.0.0
        component: microservice
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
        sidecar.istio.io/inject: "true"
    spec:
      serviceAccountName: conversion-service
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: conversion-service
        image: legacybridge/conversion-service:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 3002
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: RUST_LOG
          value: "info"
        - name: CONFIG_PATH
          value: "/etc/conversion-service/config.yaml"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: conversion-service-secrets
              key: REDIS_PASSWORD
        - name: CONVERSION_API_KEY
          valueFrom:
            secretKeyRef:
              name: conversion-service-secrets
              key: CONVERSION_API_KEY
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 2000m
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 15
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: config-volume
          mountPath: /etc/conversion-service
          readOnly: true
        - name: tmp-volume
          mountPath: /tmp
        - name: conversion-cache
          mountPath: /var/cache/conversions
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config-volume
        configMap:
          name: conversion-service-config
      - name: tmp-volume
        emptyDir:
          sizeLimit: 2Gi
      - name: conversion-cache
        emptyDir:
          sizeLimit: 5Gi
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - conversion-service
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-type
                operator: In
                values:
                - compute-optimized
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
apiVersion: v1
kind: Service
metadata:
  name: conversion-service
  namespace: legacybridge
  labels:
    app: conversion-service
    component: microservice
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 3002
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: conversion-service

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: conversion-service
  namespace: legacybridge
  labels:
    app: conversion-service
    component: rbac

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: conversion-service
  namespace: legacybridge
  labels:
    app: conversion-service
    component: rbac
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: conversion-service
  namespace: legacybridge
  labels:
    app: conversion-service
    component: rbac
subjects:
- kind: ServiceAccount
  name: conversion-service
  namespace: legacybridge
roleRef:
  kind: Role
  name: conversion-service
  apiGroup: rbac.authorization.k8s.io
