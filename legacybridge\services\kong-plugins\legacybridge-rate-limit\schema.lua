-- Schema definition for LegacyBridge rate limiting plugin

local typedefs = require "kong.db.schema.typedefs"

return {
  name = "legacybridge-rate-limit",
  fields = {
    { consumer = typedefs.no_consumer },
    { protocols = typedefs.protocols_http },
    { config = {
        type = "record",
        fields = {
          {
            namespace = {
              type = "string",
              default = "legacybridge",
              description = "Namespace for rate limit keys in Redis"
            }
          },
          {
            redis_host = {
              type = "string",
              default = "redis",
              description = "Redis host for rate limiting storage"
            }
          },
          {
            redis_port = {
              type = "number",
              default = 6379,
              description = "Redis port"
            }
          },
          {
            redis_password = {
              type = "string",
              description = "Redis password (optional)"
            }
          },
          {
            redis_timeout = {
              type = "number",
              default = 1000,
              description = "Redis connection timeout in milliseconds"
            }
          },
          {
            default_requests_per_minute = {
              type = "number",
              default = 100,
              description = "Default requests per minute limit"
            }
          },
          {
            default_requests_per_hour = {
              type = "number",
              default = 5000,
              description = "Default requests per hour limit"
            }
          },
          {
            default_requests_per_day = {
              type = "number",
              default = 50000,
              description = "Default requests per day limit"
            }
          },
          {
            service_limits = {
              type = "map",
              keys = { type = "string" },
              values = {
                type = "record",
                fields = {
                  {
                    requests_per_minute = {
                      type = "number",
                      description = "Service-specific requests per minute"
                    }
                  },
                  {
                    requests_per_hour = {
                      type = "number",
                      description = "Service-specific requests per hour"
                    }
                  },
                  {
                    requests_per_day = {
                      type = "number",
                      description = "Service-specific requests per day"
                    }
                  }
                }
              },
              description = "Service-specific rate limits"
            }
          },
          {
            admin_limit_multiplier = {
              type = "number",
              default = 10,
              description = "Rate limit multiplier for admin users"
            }
          },
          {
            premium_limit_multiplier = {
              type = "number",
              default = 5,
              description = "Rate limit multiplier for premium users"
            }
          },
          {
            pro_limit_multiplier = {
              type = "number",
              default = 3,
              description = "Rate limit multiplier for pro users"
            }
          },
          {
            enable_user_tier_limits = {
              type = "boolean",
              default = true,
              description = "Enable different limits based on user tiers"
            }
          }
        }
      }
    }
  }
}
