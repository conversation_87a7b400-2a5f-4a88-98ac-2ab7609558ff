// Shared library for LegacyBridge microservices
pub mod types;
pub mod database;
pub mod events;
pub mod service_client;
pub mod circuit_breaker;
pub mod metrics;
pub mod auth;
pub mod config;
pub mod error;
pub mod cache;
pub mod service_discovery;
pub mod resilient_client;
pub mod service_clients;
pub mod circuit_breaker_monitor;

// Re-export commonly used types
pub use types::*;
pub use error::{ServiceError, ServiceResult};
pub use auth::{Claims, UserInfo};
pub use config::ServiceConfig;
pub use database::{DatabaseManager, Repository, UserRepository};

// Re-export external dependencies for consistency
pub use axum;
pub use serde;
pub use serde_json;
pub use uuid;
pub use chrono;
pub use sqlx;
pub use redis;
pub use reqwest;
pub use anyhow;
pub use thiserror;
pub use tracing;
pub use validator;
pub use jsonwebtoken;
pub use prometheus;
