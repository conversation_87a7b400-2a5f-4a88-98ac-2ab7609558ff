# Custom Kong image with LegacyBridge plugins
FROM kong:3.4-alpine

# Install dependencies for custom plugins
USER root
RUN apk add --no-cache git

# Create plugins directory
RUN mkdir -p /usr/local/share/lua/5.1/kong/plugins

# Copy custom plugins
COPY legacybridge-auth /usr/local/share/lua/5.1/kong/plugins/legacybridge-auth
COPY legacybridge-rate-limit /usr/local/share/lua/5.1/kong/plugins/legacybridge-rate-limit
COPY legacybridge-transformer /usr/local/share/lua/5.1/kong/plugins/legacybridge-transformer

# Set proper permissions
RUN chown -R kong:kong /usr/local/share/lua/5.1/kong/plugins

# Switch back to kong user
USER kong

# Set environment variable to load custom plugins
ENV KONG_PLUGINS=bundled,legacybridge-auth,legacybridge-rate-limit,legacybridge-transformer
ENV KONG_LUA_PACKAGE_PATH=/usr/local/share/lua/5.1/?.lua;;

# Expose ports
EXPOSE 8000 8001 8002 8443 8444

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD kong health

# Start Kong
CMD ["kong", "docker-start"]
