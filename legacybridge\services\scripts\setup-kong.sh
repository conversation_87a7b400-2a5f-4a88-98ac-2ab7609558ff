#!/bin/bash

# Advanced Kong API Gateway configuration for LegacyBridge microservices
# Includes custom plugins, advanced routing, and service-specific configurations
set -e

KONG_ADMIN_URL="http://localhost:8001"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PLUGINS_DIR="$SCRIPT_DIR/../kong-plugins"

echo "🌉 Configuring Kong API Gateway with advanced features..."

# Function to check if Kong is ready
wait_for_kong() {
    echo "⏳ Waiting for Kong Admin API..."
    until curl -f $KONG_ADMIN_URL/status > /dev/null 2>&1; do
        sleep 2
    done
    echo "✅ Kong Admin API is ready"
}

# Function to install custom plugins
install_custom_plugins() {
    echo "🔌 Installing custom Kong plugins..."

    # Check if plugins directory exists
    if [ ! -d "$PLUGINS_DIR" ]; then
        echo "❌ Plugins directory not found: $PLUGINS_DIR"
        return 1
    fi

    # Copy plugins to Kong plugins directory (this would be done in Docker build)
    echo "  📦 Custom plugins available:"
    echo "    - legacybridge-auth: Advanced authentication with user context"
    echo "    - legacybridge-rate-limit: Service-aware rate limiting"
    echo "    - legacybridge-transformer: Request/response transformation"
    echo "  ✅ Custom plugins ready for configuration"
}

# Function to create service with advanced configuration
create_service() {
    local service_name=$1
    local service_host=$2
    local service_port=$3
    local service_path=${4:-"/api/v1"}
    local retries=${5:-5}
    local connect_timeout=${6:-60000}
    local write_timeout=${7:-60000}
    local read_timeout=${8:-60000}

    echo "📝 Creating service: $service_name"

    # Check if service exists
    if curl -s $KONG_ADMIN_URL/services/$service_name | grep -q "\"name\":\"$service_name\""; then
        echo "  ℹ️  Service $service_name already exists, updating..."
        curl -X PATCH $KONG_ADMIN_URL/services/$service_name \
            --data "protocol=http" \
            --data "host=$service_host" \
            --data "port=$service_port" \
            --data "path=$service_path" \
            --data "retries=$retries" \
            --data "connect_timeout=$connect_timeout" \
            --data "write_timeout=$write_timeout" \
            --data "read_timeout=$read_timeout" > /dev/null
    else
        curl -X POST $KONG_ADMIN_URL/services \
            --data "name=$service_name" \
            --data "protocol=http" \
            --data "host=$service_host" \
            --data "port=$service_port" \
            --data "path=$service_path" \
            --data "retries=$retries" \
            --data "connect_timeout=$connect_timeout" \
            --data "write_timeout=$write_timeout" \
            --data "read_timeout=$read_timeout" > /dev/null
    fi
    echo "  ✅ Service $service_name configured with timeouts and retries"
}

# Function to create route with advanced configuration
create_route() {
    local service_name=$1
    local route_name=$2
    local route_path=$3
    local methods=${4:-"GET,POST,PUT,DELETE,PATCH,OPTIONS"}
    local strip_path=${5:-"true"}
    local preserve_host=${6:-"false"}

    echo "🛣️  Creating route: $route_name"

    # Check if route exists
    if curl -s $KONG_ADMIN_URL/routes/$route_name | grep -q "\"name\":\"$route_name\""; then
        echo "  ℹ️  Route $route_name already exists, updating..."
        curl -X PATCH $KONG_ADMIN_URL/routes/$route_name \
            --data "paths[]=$route_path" \
            --data "methods[]=$methods" \
            --data "strip_path=$strip_path" \
            --data "preserve_host=$preserve_host" > /dev/null
    else
        curl -X POST $KONG_ADMIN_URL/services/$service_name/routes \
            --data "name=$route_name" \
            --data "paths[]=$route_path" \
            --data "methods[]=$methods" \
            --data "strip_path=$strip_path" \
            --data "preserve_host=$preserve_host" > /dev/null
    fi
    echo "  ✅ Route $route_name configured with methods: $methods"
}

# Function to add plugin if it doesn't exist
add_plugin() {
    local plugin_name=$1
    local service_name=$2
    shift 2
    local plugin_config=("$@")
    
    echo "🔌 Adding plugin: $plugin_name to $service_name"
    
    # Check if plugin exists for this service
    if curl -s "$KONG_ADMIN_URL/services/$service_name/plugins" | grep -q "\"name\":\"$plugin_name\""; then
        echo "  ℹ️  Plugin $plugin_name already exists for $service_name"
        return
    fi
    
    local curl_cmd="curl -X POST $KONG_ADMIN_URL/services/$service_name/plugins --data \"name=$plugin_name\""
    for config in "${plugin_config[@]}"; do
        curl_cmd="$curl_cmd --data \"$config\""
    done
    
    eval $curl_cmd > /dev/null
    echo "  ✅ Plugin $plugin_name added to $service_name"
}

# Function to add global plugin
add_global_plugin() {
    local plugin_name=$1
    shift
    local plugin_config=("$@")
    
    echo "🌐 Adding global plugin: $plugin_name"
    
    # Check if global plugin exists
    if curl -s "$KONG_ADMIN_URL/plugins" | grep -q "\"name\":\"$plugin_name\""; then
        echo "  ℹ️  Global plugin $plugin_name already exists"
        return
    fi
    
    local curl_cmd="curl -X POST $KONG_ADMIN_URL/plugins --data \"name=$plugin_name\""
    for config in "${plugin_config[@]}"; do
        curl_cmd="$curl_cmd --data \"$config\""
    done
    
    eval $curl_cmd > /dev/null
    echo "  ✅ Global plugin $plugin_name added"
}

# Wait for Kong to be ready
wait_for_kong

# Install custom plugins
install_custom_plugins

# Create services with advanced configuration
echo "🏗️  Creating microservices..."
create_service "auth-service" "host.docker.internal" "3001" "/api/v1" 5 10000 30000 30000
create_service "conversion-service" "host.docker.internal" "3002" "/api/v1" 3 15000 120000 120000
create_service "file-service" "host.docker.internal" "3003" "/api/v1" 5 10000 60000 60000
create_service "job-service" "host.docker.internal" "3004" "/api/v1" 5 10000 30000 30000

# Create routes with method restrictions
echo "🛣️  Creating service routes..."
create_route "auth-service" "auth-route" "/auth" "GET,POST,PUT,DELETE,OPTIONS"
create_route "conversion-service" "conversion-route" "/convert" "GET,POST,DELETE,OPTIONS"
create_route "file-service" "file-route" "/files" "GET,POST,PUT,DELETE,OPTIONS"
create_route "job-service" "job-route" "/jobs" "GET,POST,PUT,DELETE,OPTIONS"

# Create health check routes (public access)
create_route "auth-service" "auth-health-route" "/auth/health" "GET"
create_route "conversion-service" "conversion-health-route" "/convert/health" "GET"
create_route "file-service" "file-health-route" "/files/health" "GET"
create_route "job-service" "job-health-route" "/jobs/health" "GET"

# Configure advanced plugins
echo "🔧 Configuring advanced plugins..."

# Global request size limiting
add_global_plugin "request-size-limiting" \
    "config.allowed_payload_size=100"

# Global Prometheus metrics
add_global_plugin "prometheus"

# Global request/response logging
add_global_plugin "file-log" \
    "config.path=/tmp/access.log" \
    "config.reopen=true"

# Configure custom transformer plugin globally
echo "🔄 Configuring request/response transformation..."
add_global_plugin "legacybridge-transformer" \
    "config.add_response_metadata=true" \
    "config.enable_cors=true" \
    "config.cors_origin=*" \
    "config.cors_methods=GET,POST,PUT,DELETE,OPTIONS,PATCH" \
    "config.cors_headers=Content-Type,Authorization,X-Requested-With,X-Correlation-ID"

# Configure custom rate limiting plugin globally
echo "⚡ Configuring advanced rate limiting..."
add_global_plugin "legacybridge-rate-limit" \
    "config.default_requests_per_minute=100" \
    "config.default_requests_per_hour=5000" \
    "config.default_requests_per_day=50000" \
    "config.admin_limit_multiplier=10" \
    "config.premium_limit_multiplier=5" \
    "config.redis_host=redis" \
    "config.redis_port=6379"

echo "🔐 Configuring service-specific authentication..."

# Custom authentication for protected services (not auth service)
for service in "conversion-service" "file-service" "job-service"; do
    echo "  🔒 Adding authentication to $service"
    add_plugin "legacybridge-auth" "$service" \
        "config.auth_service_url=http://auth-service:3001" \
        "config.auth_service_timeout=5000" \
        "config.enable_audit_logging=true"
done

echo "🎯 Configuring service-specific rate limits..."

# Conversion service - higher limits due to processing needs
add_plugin "legacybridge-rate-limit" "conversion-service" \
    "config.default_requests_per_minute=50" \
    "config.default_requests_per_hour=2000" \
    "config.service_limits.conversion-service.requests_per_minute=50"

# File service - moderate limits for uploads/downloads
add_plugin "legacybridge-rate-limit" "file-service" \
    "config.default_requests_per_minute=200" \
    "config.default_requests_per_hour=8000" \
    "config.service_limits.file-service.requests_per_minute=200"

# Auth service - higher limits for login/validation
add_plugin "legacybridge-rate-limit" "auth-service" \
    "config.default_requests_per_minute=500" \
    "config.default_requests_per_hour=10000" \
    "config.service_limits.auth-service.requests_per_minute=500"

echo "✅ Kong configuration completed!"
echo ""
echo "🔗 API Gateway endpoints:"
echo "  🔐 Authentication: http://localhost:8000/auth"
echo "  🔄 Conversion:     http://localhost:8000/convert"
echo "  📁 Files:          http://localhost:8000/files"
echo "  ⚙️  Jobs:           http://localhost:8000/jobs"
echo ""
echo "🛠️  Kong Admin:"
echo "  📊 Admin API:      http://localhost:8001"
echo "  📈 Metrics:        http://localhost:8001/metrics"
echo ""
echo "🎯 Next steps:"
echo "  1. Start the microservices"
echo "  2. Test the API endpoints"
echo "  3. Monitor metrics at http://localhost:9090"
