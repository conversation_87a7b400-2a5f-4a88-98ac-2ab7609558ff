-- Performance monitoring and audit trail tables for enterprise features
-- Migration 002: Add performance metrics and audit events tables

-- Performance metrics table for monitoring database operations
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_name VARCHAR(255) NOT NULL,
    metric_value DOUBLE PRECISION NOT NULL,
    labels JSONB,
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Indexes for efficient querying
    CONSTRAINT performance_metrics_metric_name_check CHECK (length(metric_name) > 0),
    CONSTRAINT performance_metrics_metric_value_check CHECK (metric_value >= 0)
);

-- Indexes for performance metrics
CREATE INDEX idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX idx_performance_metrics_recorded_at ON performance_metrics(recorded_at);
CREATE INDEX idx_performance_metrics_labels ON performance_metrics USING GIN(labels);
CREATE INDEX idx_performance_metrics_name_time ON performance_metrics(metric_name, recorded_at);

-- Audit events table for compliance and security monitoring
CREATE TABLE audit_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    event_type VARCHAR(255) NOT NULL,
    event_data JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT audit_events_event_type_check CHECK (length(event_type) > 0)
);

-- Indexes for audit events
CREATE INDEX idx_audit_events_user_id ON audit_events(user_id);
CREATE INDEX idx_audit_events_event_type ON audit_events(event_type);
CREATE INDEX idx_audit_events_created_at ON audit_events(created_at);
CREATE INDEX idx_audit_events_user_event ON audit_events(user_id, event_type);
CREATE INDEX idx_audit_events_event_data ON audit_events USING GIN(event_data);
CREATE INDEX idx_audit_events_ip_address ON audit_events(ip_address);

-- Database connection pool monitoring table
CREATE TABLE connection_pool_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_name VARCHAR(255) NOT NULL,
    pool_size INTEGER NOT NULL,
    active_connections INTEGER NOT NULL,
    idle_connections INTEGER NOT NULL,
    pending_requests INTEGER NOT NULL DEFAULT 0,
    recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT connection_pool_metrics_pool_size_check CHECK (pool_size >= 0),
    CONSTRAINT connection_pool_metrics_active_check CHECK (active_connections >= 0),
    CONSTRAINT connection_pool_metrics_idle_check CHECK (idle_connections >= 0),
    CONSTRAINT connection_pool_metrics_pending_check CHECK (pending_requests >= 0)
);

-- Indexes for connection pool metrics
CREATE INDEX idx_connection_pool_metrics_service ON connection_pool_metrics(service_name);
CREATE INDEX idx_connection_pool_metrics_recorded_at ON connection_pool_metrics(recorded_at);
CREATE INDEX idx_connection_pool_metrics_service_time ON connection_pool_metrics(service_name, recorded_at);

-- Query performance tracking table
CREATE TABLE query_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_hash VARCHAR(64) NOT NULL, -- Hash of the query for grouping
    query_type VARCHAR(50) NOT NULL, -- SELECT, INSERT, UPDATE, DELETE
    table_name VARCHAR(255),
    duration_ms DOUBLE PRECISION NOT NULL,
    rows_affected INTEGER,
    service_name VARCHAR(255) NOT NULL,
    executed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT query_performance_duration_check CHECK (duration_ms >= 0),
    CONSTRAINT query_performance_rows_check CHECK (rows_affected IS NULL OR rows_affected >= 0)
);

-- Indexes for query performance
CREATE INDEX idx_query_performance_hash ON query_performance(query_hash);
CREATE INDEX idx_query_performance_type ON query_performance(query_type);
CREATE INDEX idx_query_performance_table ON query_performance(table_name);
CREATE INDEX idx_query_performance_service ON query_performance(service_name);
CREATE INDEX idx_query_performance_executed_at ON query_performance(executed_at);
CREATE INDEX idx_query_performance_duration ON query_performance(duration_ms);
CREATE INDEX idx_query_performance_service_table ON query_performance(service_name, table_name);

-- Database health check results table
CREATE TABLE health_check_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_name VARCHAR(255) NOT NULL,
    check_type VARCHAR(100) NOT NULL, -- 'connection', 'query', 'migration'
    status VARCHAR(50) NOT NULL, -- 'healthy', 'degraded', 'unhealthy'
    response_time_ms DOUBLE PRECISION,
    error_message TEXT,
    details JSONB,
    checked_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT health_check_status_check CHECK (status IN ('healthy', 'degraded', 'unhealthy')),
    CONSTRAINT health_check_response_time_check CHECK (response_time_ms IS NULL OR response_time_ms >= 0)
);

-- Indexes for health check results
CREATE INDEX idx_health_check_service ON health_check_results(service_name);
CREATE INDEX idx_health_check_type ON health_check_results(check_type);
CREATE INDEX idx_health_check_status ON health_check_results(status);
CREATE INDEX idx_health_check_checked_at ON health_check_results(checked_at);
CREATE INDEX idx_health_check_service_status ON health_check_results(service_name, status);

-- Create a view for recent performance metrics (last 24 hours)
CREATE VIEW recent_performance_metrics AS
SELECT 
    metric_name,
    AVG(metric_value) as avg_value,
    MIN(metric_value) as min_value,
    MAX(metric_value) as max_value,
    COUNT(*) as sample_count,
    DATE_TRUNC('hour', recorded_at) as hour_bucket
FROM performance_metrics 
WHERE recorded_at >= NOW() - INTERVAL '24 hours'
GROUP BY metric_name, DATE_TRUNC('hour', recorded_at)
ORDER BY hour_bucket DESC, metric_name;

-- Create a view for audit event summary
CREATE VIEW audit_event_summary AS
SELECT 
    event_type,
    COUNT(*) as event_count,
    COUNT(DISTINCT user_id) as unique_users,
    DATE_TRUNC('day', created_at) as event_date
FROM audit_events 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY event_type, DATE_TRUNC('day', created_at)
ORDER BY event_date DESC, event_count DESC;

-- Create a view for database health overview
CREATE VIEW database_health_overview AS
SELECT 
    service_name,
    check_type,
    status,
    COUNT(*) as check_count,
    AVG(response_time_ms) as avg_response_time,
    MAX(checked_at) as last_check
FROM health_check_results 
WHERE checked_at >= NOW() - INTERVAL '1 hour'
GROUP BY service_name, check_type, status
ORDER BY service_name, check_type;

-- Add comments for documentation
COMMENT ON TABLE performance_metrics IS 'Stores performance metrics for database operations and system monitoring';
COMMENT ON TABLE audit_events IS 'Stores audit trail events for compliance and security monitoring';
COMMENT ON TABLE connection_pool_metrics IS 'Tracks database connection pool statistics for performance monitoring';
COMMENT ON TABLE query_performance IS 'Records individual query performance metrics for optimization';
COMMENT ON TABLE health_check_results IS 'Stores results of database health checks for monitoring';

COMMENT ON VIEW recent_performance_metrics IS 'Aggregated performance metrics for the last 24 hours';
COMMENT ON VIEW audit_event_summary IS 'Summary of audit events for the last 30 days';
COMMENT ON VIEW database_health_overview IS 'Current database health status across all services';

-- Create function to clean up old performance metrics (retention policy)
CREATE OR REPLACE FUNCTION cleanup_old_performance_metrics()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete performance metrics older than 90 days
    DELETE FROM performance_metrics 
    WHERE recorded_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup
    INSERT INTO audit_events (user_id, event_type, event_data)
    VALUES (
        NULL, 
        'system_cleanup',
        jsonb_build_object(
            'table', 'performance_metrics',
            'deleted_count', deleted_count,
            'retention_days', 90
        )
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old audit events (retention policy)
CREATE OR REPLACE FUNCTION cleanup_old_audit_events()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete audit events older than 2 years (compliance requirement)
    DELETE FROM audit_events 
    WHERE created_at < NOW() - INTERVAL '2 years';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Grant appropriate permissions
GRANT SELECT, INSERT ON performance_metrics TO legacybridge_app;
GRANT SELECT, INSERT ON audit_events TO legacybridge_app;
GRANT SELECT, INSERT ON connection_pool_metrics TO legacybridge_app;
GRANT SELECT, INSERT ON query_performance TO legacybridge_app;
GRANT SELECT, INSERT ON health_check_results TO legacybridge_app;

GRANT SELECT ON recent_performance_metrics TO legacybridge_app;
GRANT SELECT ON audit_event_summary TO legacybridge_app;
GRANT SELECT ON database_health_overview TO legacybridge_app;
