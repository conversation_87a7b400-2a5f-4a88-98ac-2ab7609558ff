#!/bin/bash

# Start infrastructure services for LegacyBridge microservices
set -e

echo "🚀 Starting LegacyBridge infrastructure services..."

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Create monitoring configuration if it doesn't exist
mkdir -p monitoring
if [ ! -f monitoring/prometheus.yml ]; then
    echo "📝 Creating Prometheus configuration..."
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'auth-service'
    static_configs:
      - targets: ['host.docker.internal:3001']
    metrics_path: '/metrics'

  - job_name: 'conversion-service'
    static_configs:
      - targets: ['host.docker.internal:3002']
    metrics_path: '/metrics'

  - job_name: 'file-service'
    static_configs:
      - targets: ['host.docker.internal:3003']
    metrics_path: '/metrics'

  - job_name: 'job-service'
    static_configs:
      - targets: ['host.docker.internal:3004']
    metrics_path: '/metrics'
EOF
fi

# Start infrastructure services
echo "🐳 Starting infrastructure containers..."
docker-compose -f docker-compose.infrastructure.yml up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."

# Wait for PostgreSQL
echo "  📊 Waiting for PostgreSQL..."
until docker exec legacybridge-postgres pg_isready -U postgres > /dev/null 2>&1; do
    sleep 2
done
echo "  ✅ PostgreSQL is ready"

# Wait for Redis
echo "  🔄 Waiting for Redis..."
until docker exec legacybridge-redis redis-cli ping > /dev/null 2>&1; do
    sleep 2
done
echo "  ✅ Redis is ready"

# Wait for MinIO
echo "  📦 Waiting for MinIO..."
until curl -f http://localhost:9000/minio/health/live > /dev/null 2>&1; do
    sleep 2
done
echo "  ✅ MinIO is ready"

# Wait for Kong
echo "  🌉 Waiting for Kong..."
until curl -f http://localhost:8001/status > /dev/null 2>&1; do
    sleep 2
done
echo "  ✅ Kong is ready"

# Create MinIO bucket for file storage
echo "📦 Setting up MinIO bucket..."
docker run --rm --network services_legacybridge-network \
    minio/mc:latest \
    sh -c "
        mc alias set minio http://minio:9000 minioadmin minioadmin &&
        mc mb minio/legacybridge-files --ignore-existing &&
        mc policy set public minio/legacybridge-files
    "

echo "✅ Infrastructure services are ready!"
echo ""
echo "🔗 Service URLs:"
echo "  📊 PostgreSQL:     localhost:5432 (user: postgres, password: postgres)"
echo "  🔄 Redis:          localhost:6379"
echo "  📦 MinIO:          http://localhost:9000 (admin: minioadmin/minioadmin)"
echo "  🌉 Kong Admin:     http://localhost:8001"
echo "  📈 Prometheus:     http://localhost:9090"
echo "  📊 Grafana:        http://localhost:3000 (admin/admin)"
echo "  🔍 Jaeger:         http://localhost:16686"
echo "  📋 Kibana:         http://localhost:5601"
echo ""
echo "🎯 Next steps:"
echo "  1. Run './scripts/setup-kong.sh' to configure API Gateway"
echo "  2. Start individual services with 'cargo run' in each service directory"
echo "  3. Run tests with './scripts/test-all-services.sh'"
