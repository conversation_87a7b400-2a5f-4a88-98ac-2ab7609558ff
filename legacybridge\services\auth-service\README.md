# Authentication Service

The Authentication Service is a core microservice in the LegacyBridge architecture responsible for user authentication, JWT token management, and user management operations.

## Features

- **User Authentication**: Login/logout with JWT tokens
- **Token Management**: JWT token generation, validation, and refresh
- **User Management**: CRUD operations for user accounts
- **Role-Based Access Control**: Support for user roles and permissions
- **Session Management**: Redis-based session storage
- **Security**: Password hashing with Argon2, secure token handling
- **Monitoring**: Prometheus metrics, distributed tracing, health checks
- **Audit Trail**: Complete audit logging of authentication events

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh JWT token
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/validate` - Validate JWT token (for other services)

### User Management
- `GET /api/v1/users` - List users (paginated)
- `POST /api/v1/users` - Create new user
- `GET /api/v1/users/:id` - Get user by ID
- `PUT /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user (soft delete)

### Profile Management
- `GET /api/v1/profile` - Get current user profile
- `PUT /api/v1/profile` - Update current user profile
- `PUT /api/v1/profile/password` - Change password

### Health & Monitoring
- `GET /health` - Basic health check
- `GET /ready` - Readiness check (Kubernetes)
- `GET /status` - Detailed status with metrics
- `GET /metrics` - Prometheus metrics (port 9091)

## Configuration

The service uses environment variables for configuration. Copy `.env.example` to `.env` and adjust values:

```bash
cp .env.example .env
```

Key configuration options:
- `LEGACYBRIDGE_SERVICE_PORT`: Service port (default: 3001)
- `LEGACYBRIDGE_DATABASE_URL`: PostgreSQL connection string
- `LEGACYBRIDGE_REDIS_URL`: Redis connection string
- `LEGACYBRIDGE_AUTH_JWT_SECRET`: JWT signing secret (change in production!)

## Dependencies

### Infrastructure
- **PostgreSQL 14+**: Primary database
- **Redis 6+**: Session storage and caching
- **Jaeger**: Distributed tracing (optional)

### External Services
- None (this is a foundational service)

## Development

### Prerequisites
```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Start infrastructure services
cd ../
./scripts/start-infrastructure.sh
```

### Running the Service
```bash
# Development mode
cargo run

# With environment variables
LEGACYBRIDGE_SERVICE_PORT=3001 cargo run

# Production mode
cargo run --release
```

### Testing
```bash
# Unit tests
cargo test

# Integration tests
cargo test --test integration_tests

# With test output
cargo test -- --nocapture
```

### Database Migrations
```bash
# Run migrations (automatic on startup)
sqlx migrate run --database-url $LEGACYBRIDGE_DATABASE_URL
```

## Docker

### Build Image
```bash
docker build -t legacybridge/auth-service .
```

### Run Container
```bash
docker run -p 3001:3001 \
  -e LEGACYBRIDGE_DATABASE_URL=postgresql://postgres:<EMAIL>:5432/legacybridge \
  -e LEGACYBRIDGE_REDIS_URL=redis://host.docker.internal:6379 \
  legacybridge/auth-service
```

## Monitoring

### Metrics
The service exposes Prometheus metrics on port 9091:
- Request counts and durations
- Authentication events and failures
- Database and Redis connection metrics
- System resource usage

### Health Checks
- `/health`: Basic health status
- `/ready`: Kubernetes readiness probe
- `/status`: Detailed status with metrics

### Tracing
Distributed tracing is available via Jaeger. Set `JAEGER_AGENT_ENDPOINT` to enable.

## Security

### Authentication Flow
1. User submits credentials to `/auth/login`
2. Service validates credentials against database
3. On success, generates JWT access token and refresh token
4. Tokens are returned to client
5. Client includes access token in `Authorization: Bearer <token>` header
6. Other services validate tokens via `/auth/validate` endpoint

### Password Security
- Passwords are hashed using Argon2id
- Minimum password length: 8 characters
- Password complexity requirements can be configured

### Token Security
- JWT tokens are signed with HS256
- Access tokens expire in 1 hour (configurable)
- Refresh tokens expire in 7 days (configurable)
- Sessions are stored in Redis for revocation support

## Deployment

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: legacybridge/auth-service:latest
        ports:
        - containerPort: 3001
        env:
        - name: LEGACYBRIDGE_DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
        readinessProbe:
          httpGet:
            path: /ready
            port: 3001
```

### Environment Variables
See `.env.example` for all available configuration options.

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running and accessible
   - Verify connection string format
   - Ensure database exists and migrations are applied

2. **Redis Connection Failed**
   - Check Redis is running and accessible
   - Verify Redis URL format
   - Check network connectivity

3. **JWT Token Invalid**
   - Verify JWT secret is consistent across services
   - Check token expiration
   - Ensure session exists in Redis

### Logs
The service uses structured JSON logging. Key log fields:
- `level`: Log level (error, warn, info, debug)
- `timestamp`: ISO 8601 timestamp
- `service`: Service name
- `user_id`: User ID (when available)
- `request_id`: Unique request identifier

### Debug Mode
Enable debug logging:
```bash
RUST_LOG=debug cargo run
```
