// Resilient service client with circuit breakers and fallback strategies
use crate::circuit_breaker::{CircuitBreaker, CircuitBreakerConfig, CircuitBreakerRegistry};
use crate::cache::CacheManager;
use crate::error::{ServiceError, ServiceResult};
use crate::service_discovery::{ServiceRegistry, LoadBalancingStrategy};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Duration;
use std::collections::HashMap;

pub struct ResilientServiceClient {
    client: Client,
    circuit_breaker_registry: Arc<CircuitBreakerRegistry>,
    cache: Arc<CacheManager>,
    service_registry: Arc<ServiceRegistry>,
    fallback_strategies: HashMap<String, Box<dyn FallbackStrategy + Send + Sync>>,
    auth_token: Option<String>,
}

impl ResilientServiceClient {
    pub fn new(
        circuit_breaker_registry: Arc<CircuitBreakerRegistry>,
        cache: Arc<CacheManager>,
        service_registry: Arc<ServiceRegistry>,
    ) -> Self {
        Self {
            client: Client::builder()
                .timeout(Duration::from_secs(30))
                .build()
                .unwrap(),
            circuit_breaker_registry,
            cache,
            service_registry,
            fallback_strategies: HashMap::new(),
            auth_token: None,
        }
    }

    pub fn with_auth_token(mut self, token: String) -> Self {
        self.auth_token = Some(token);
        self
    }

    pub fn register_fallback<F>(&mut self, service_name: String, fallback: F)
    where
        F: FallbackStrategy + Send + Sync + 'static,
    {
        self.fallback_strategies.insert(service_name, Box::new(fallback));
    }

    /// Make a resilient HTTP request with circuit breaker, caching, and fallback
    pub async fn request_with_fallback<T, R>(
        &self,
        service_name: &str,
        method: &str,
        path: &str,
        body: Option<&T>,
        cache_key: Option<&str>,
        cache_ttl: Option<Duration>,
    ) -> ServiceResult<R>
    where
        T: Serialize,
        R: for<'de> Deserialize<'de> + Clone + Send + Sync + 'static,
    {
        // Try to get from cache first if cache_key is provided
        if let Some(key) = cache_key {
            if let Ok(Some(cached_value)) = self.cache.get::<R>(key).await {
                tracing::debug!("Cache hit for key: {}", key);
                return Ok(cached_value);
            }
        }

        // Get service instance using service discovery
        let service_instance = self
            .service_registry
            .get_instance(service_name, LoadBalancingStrategy::RoundRobin)
            .await?;

        let instance = match service_instance {
            Some(inst) => inst,
            None => {
                tracing::warn!("No healthy instances found for service: {}", service_name);
                return self.execute_fallback(service_name, method, path, body).await;
            }
        };

        let url = format!("{}{}", instance.endpoint(), path);
        let circuit_breaker = self.circuit_breaker_registry.get_or_create(service_name);

        // Execute request with circuit breaker
        let result = circuit_breaker
            .call(|| async {
                self.make_http_request(&url, method, body).await
            })
            .await;

        match result {
            Ok(response) => {
                // Cache successful response if cache_key is provided
                if let (Some(key), Some(ttl)) = (cache_key, cache_ttl) {
                    if let Err(e) = self.cache.set_with_ttl(key, &response, ttl).await {
                        tracing::warn!("Failed to cache response: {}", e);
                    }
                }
                Ok(response)
            }
            Err(ServiceError::CircuitBreakerOpen) => {
                tracing::warn!("Circuit breaker open for service: {}", service_name);
                self.execute_fallback(service_name, method, path, body).await
            }
            Err(e) => {
                tracing::error!("Request failed for service {}: {}", service_name, e);
                self.execute_fallback(service_name, method, path, body).await
            }
        }
    }

    async fn make_http_request<T, R>(
        &self,
        url: &str,
        method: &str,
        body: Option<&T>,
    ) -> ServiceResult<R>
    where
        T: Serialize,
        R: for<'de> Deserialize<'de>,
    {
        let mut request_builder = match method.to_uppercase().as_str() {
            "GET" => self.client.get(url),
            "POST" => {
                let mut builder = self.client.post(url);
                if let Some(b) = body {
                    builder = builder.json(b);
                }
                builder
            }
            "PUT" => {
                let mut builder = self.client.put(url);
                if let Some(b) = body {
                    builder = builder.json(b);
                }
                builder
            }
            "DELETE" => self.client.delete(url),
            _ => return Err(ServiceError::InvalidRequest("Unsupported HTTP method".to_string())),
        };

        if let Some(token) = &self.auth_token {
            request_builder = request_builder.bearer_auth(token);
        }

        let response = request_builder.send().await?;

        if response.status().is_success() {
            let data = response.json::<R>().await?;
            Ok(data)
        } else {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            Err(ServiceError::HttpClient(reqwest::Error::from(
                std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("HTTP {}: {}", status, error_text),
                ),
            )))
        }
    }

    async fn execute_fallback<T, R>(
        &self,
        service_name: &str,
        method: &str,
        path: &str,
        body: Option<&T>,
    ) -> ServiceResult<R>
    where
        T: Serialize,
        R: for<'de> Deserialize<'de> + Clone + Send + Sync + 'static,
    {
        if let Some(fallback) = self.fallback_strategies.get(service_name) {
            tracing::info!("Executing fallback strategy for service: {}", service_name);
            fallback.execute(method, path, body, &self.cache).await
        } else {
            tracing::error!("No fallback strategy available for service: {}", service_name);
            Err(ServiceError::ServiceUnavailable(format!(
                "Service {} is unavailable and no fallback is configured",
                service_name
            )))
        }
    }

    /// Get circuit breaker statistics for all services
    pub fn get_circuit_breaker_stats(&self) -> HashMap<String, crate::circuit_breaker::CircuitBreakerStats> {
        self.circuit_breaker_registry.get_all_stats()
    }
}

/// Trait for implementing fallback strategies
#[async_trait::async_trait]
pub trait FallbackStrategy {
    async fn execute<T, R>(
        &self,
        method: &str,
        path: &str,
        body: Option<&T>,
        cache: &CacheManager,
    ) -> ServiceResult<R>
    where
        T: Serialize + Send + Sync,
        R: for<'de> Deserialize<'de> + Clone + Send + Sync + 'static;
}

/// Cache-based fallback strategy
pub struct CacheFallbackStrategy {
    cache_prefix: String,
    default_ttl: Duration,
}

impl CacheFallbackStrategy {
    pub fn new(cache_prefix: String, default_ttl: Duration) -> Self {
        Self {
            cache_prefix,
            default_ttl,
        }
    }
}

#[async_trait::async_trait]
impl FallbackStrategy for CacheFallbackStrategy {
    async fn execute<T, R>(
        &self,
        method: &str,
        path: &str,
        _body: Option<&T>,
        cache: &CacheManager,
    ) -> ServiceResult<R>
    where
        T: Serialize + Send + Sync,
        R: for<'de> Deserialize<'de> + Clone + Send + Sync + 'static,
    {
        // For GET requests, try to return cached data
        if method.to_uppercase() == "GET" {
            let cache_key = format!("{}:{}:{}", self.cache_prefix, method, path);
            
            if let Ok(Some(cached_value)) = cache.get::<R>(&cache_key).await {
                tracing::info!("Fallback: returning cached value for {}", cache_key);
                return Ok(cached_value);
            }
        }

        Err(ServiceError::ServiceUnavailable(
            "No cached data available for fallback".to_string(),
        ))
    }
}

/// Static response fallback strategy
pub struct StaticResponseFallbackStrategy<R> {
    response: R,
}

impl<R> StaticResponseFallbackStrategy<R>
where
    R: Clone + Send + Sync + 'static,
{
    pub fn new(response: R) -> Self {
        Self { response }
    }
}

#[async_trait::async_trait]
impl<R> FallbackStrategy for StaticResponseFallbackStrategy<R>
where
    R: for<'de> Deserialize<'de> + Clone + Send + Sync + 'static,
{
    async fn execute<T, Resp>(
        &self,
        _method: &str,
        _path: &str,
        _body: Option<&T>,
        _cache: &CacheManager,
    ) -> ServiceResult<Resp>
    where
        T: Serialize + Send + Sync,
        Resp: for<'de> Deserialize<'de> + Clone + Send + Sync + 'static,
    {
        // This is a bit of a hack to work around the type system
        // In practice, you'd want to ensure R and Resp are the same type
        let json = serde_json::to_string(&self.response)?;
        let response = serde_json::from_str(&json)?;
        Ok(response)
    }
}

/// Degraded service fallback strategy
pub struct DegradedServiceFallbackStrategy {
    degraded_response_builder: Box<dyn Fn(&str, &str) -> serde_json::Value + Send + Sync>,
}

impl DegradedServiceFallbackStrategy {
    pub fn new<F>(builder: F) -> Self
    where
        F: Fn(&str, &str) -> serde_json::Value + Send + Sync + 'static,
    {
        Self {
            degraded_response_builder: Box::new(builder),
        }
    }
}

#[async_trait::async_trait]
impl FallbackStrategy for DegradedServiceFallbackStrategy {
    async fn execute<T, R>(
        &self,
        method: &str,
        path: &str,
        _body: Option<&T>,
        _cache: &CacheManager,
    ) -> ServiceResult<R>
    where
        T: Serialize + Send + Sync,
        R: for<'de> Deserialize<'de> + Clone + Send + Sync + 'static,
    {
        let degraded_response = (self.degraded_response_builder)(method, path);
        let response = serde_json::from_value(degraded_response)?;
        Ok(response)
    }
}
