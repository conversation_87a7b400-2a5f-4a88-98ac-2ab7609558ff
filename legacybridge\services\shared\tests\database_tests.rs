// Enterprise-grade database layer tests
// Tests connection pooling, repository patterns, audit trails, and performance monitoring

use legacybridge_shared::{
    database::{DatabaseManager, Repository, UserRepository, CreateUserRequest, UpdateUserRequest},
    types::User,
    ServiceResult,
};
use sqlx::PgPool;
use std::time::Duration;
use uuid::Uuid;

// Test configuration for enterprise scenarios
const TEST_DATABASE_URL: &str = "postgresql://postgres:test@localhost:5432/legacybridge_test";
const ENTERPRISE_LOAD_USERS: usize = 1000;
const CONCURRENT_CONNECTIONS: usize = 50;

#[tokio::test]
async fn test_database_manager_initialization() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    // Verify pool configuration
    let pool = db_manager.pool();
    assert!(pool.size() >= 5, "Pool should have minimum connections");
    
    // Test health check
    db_manager.health_check().await
        .expect("Health check should pass");
}

#[tokio::test]
async fn test_connection_pool_configuration() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    let pool = db_manager.pool();
    
    // Test pool limits
    assert!(pool.size() >= 5, "Should have minimum connections");
    assert!(pool.size() <= 20, "Should not exceed maximum connections");
    
    // Test connection acquisition
    let conn = pool.acquire().await
        .expect("Should be able to acquire connection");
    
    // Verify connection is valid
    let result: i32 = sqlx::query_scalar("SELECT 1")
        .fetch_one(&mut *conn)
        .await
        .expect("Connection should be valid");
    
    assert_eq!(result, 1);
}

#[tokio::test]
async fn test_enterprise_connection_pooling() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    let pool = db_manager.pool();
    
    // Test concurrent connection acquisition
    let mut handles = Vec::new();
    
    for i in 0..CONCURRENT_CONNECTIONS {
        let pool_clone = pool.clone();
        let handle = tokio::spawn(async move {
            let conn = pool_clone.acquire().await
                .expect(&format!("Connection {} should be acquired", i));
            
            // Simulate work
            let result: i32 = sqlx::query_scalar("SELECT $1")
                .bind(i as i32)
                .fetch_one(&mut *conn)
                .await
                .expect("Query should succeed");
            
            assert_eq!(result, i as i32);
            result
        });
        handles.push(handle);
    }
    
    // Wait for all connections to complete
    let results = futures::future::join_all(handles).await;
    
    // Verify all connections succeeded
    for (i, result) in results.into_iter().enumerate() {
        let value = result.expect("Task should complete successfully");
        assert_eq!(value, i as i32);
    }
}

#[tokio::test]
async fn test_user_repository_crud_operations() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    let user_repo = UserRepository::new(db_manager.pool().clone());
    
    // Test create user
    let create_request = CreateUserRequest {
        username: "test_user_crud".to_string(),
        email: "<EMAIL>".to_string(),
        password: "secure_password123".to_string(),
        roles: vec!["user".to_string(), "tester".to_string()],
    };
    
    let created_user = user_repo.create(create_request).await
        .expect("User creation should succeed");
    
    assert_eq!(created_user.username, "test_user_crud");
    assert_eq!(created_user.email, "<EMAIL>");
    assert_eq!(created_user.roles, vec!["user", "tester"]);
    assert!(created_user.is_active);
    
    // Test find by ID
    let found_user = user_repo.find_by_id(created_user.id).await
        .expect("Find by ID should succeed")
        .expect("User should exist");
    
    assert_eq!(found_user.id, created_user.id);
    assert_eq!(found_user.username, created_user.username);
    
    // Test find by username
    let found_by_username = user_repo.find_by_username("test_user_crud").await
        .expect("Find by username should succeed")
        .expect("User should exist");
    
    assert_eq!(found_by_username.id, created_user.id);
    
    // Test find by email
    let found_by_email = user_repo.find_by_email("<EMAIL>").await
        .expect("Find by email should succeed")
        .expect("User should exist");
    
    assert_eq!(found_by_email.id, created_user.id);
    
    // Test update user
    let update_request = UpdateUserRequest {
        username: Some("updated_user_crud".to_string()),
        email: Some("<EMAIL>".to_string()),
        roles: Some(vec!["user".to_string(), "admin".to_string()]),
        is_active: Some(true),
    };
    
    let updated_user = user_repo.update(created_user.id, update_request).await
        .expect("User update should succeed");
    
    assert_eq!(updated_user.username, "updated_user_crud");
    assert_eq!(updated_user.email, "<EMAIL>");
    assert_eq!(updated_user.roles, vec!["user", "admin"]);
    
    // Test list users
    let users = user_repo.list(10, 0).await
        .expect("List users should succeed");
    
    assert!(!users.is_empty(), "Should have at least one user");
    
    // Test count users
    let count = user_repo.count().await
        .expect("Count users should succeed");
    
    assert!(count > 0, "Should have at least one user");
    
    // Test delete user
    user_repo.delete(created_user.id).await
        .expect("User deletion should succeed");
    
    // Verify user is deleted
    let deleted_user = user_repo.find_by_id(created_user.id).await
        .expect("Find should succeed");
    
    assert!(deleted_user.is_none(), "User should be deleted");
}

#[tokio::test]
async fn test_enterprise_performance_monitoring() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    // Test performance metric recording
    let start_time = std::time::Instant::now();
    
    db_manager.record_performance_metric(
        "test_query_duration",
        42.5,
        Some(&serde_json::json!({
            "service": "test",
            "operation": "user_lookup"
        }))
    ).await.expect("Performance metric recording should succeed");
    
    let duration = start_time.elapsed();
    
    // Performance recording should be fast (< 10ms for enterprise requirements)
    assert!(duration < Duration::from_millis(10), 
           "Performance metric recording took too long: {:?}", duration);
    
    // Verify metric was recorded
    let metric_count: i64 = sqlx::query_scalar(
        "SELECT COUNT(*) FROM performance_metrics WHERE metric_name = 'test_query_duration'"
    )
    .fetch_one(db_manager.pool())
    .await
    .expect("Metric count query should succeed");
    
    assert!(metric_count > 0, "Performance metric should be recorded");
}

#[tokio::test]
async fn test_audit_trail_functionality() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    let user_id = Uuid::new_v4();
    
    // Test audit trail recording
    db_manager.record_audit_event(
        user_id,
        "user_login",
        Some(&serde_json::json!({
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0...",
            "success": true
        }))
    ).await.expect("Audit event recording should succeed");
    
    // Verify audit event was recorded
    let audit_count: i64 = sqlx::query_scalar(
        "SELECT COUNT(*) FROM audit_events WHERE user_id = $1 AND event_type = 'user_login'"
    )
    .bind(user_id)
    .fetch_one(db_manager.pool())
    .await
    .expect("Audit count query should succeed");
    
    assert!(audit_count > 0, "Audit event should be recorded");
}

#[tokio::test]
async fn test_enterprise_load_handling() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    let user_repo = UserRepository::new(db_manager.pool().clone());
    
    let start_time = std::time::Instant::now();
    
    // Create many users concurrently to test enterprise load
    let mut handles = Vec::new();
    
    for i in 0..ENTERPRISE_LOAD_USERS {
        let repo_clone = user_repo.clone();
        let handle = tokio::spawn(async move {
            let create_request = CreateUserRequest {
                username: format!("load_test_user_{}", i),
                email: format!("load_test_{}@example.com", i),
                password: "password123".to_string(),
                roles: vec!["user".to_string()],
            };
            
            repo_clone.create(create_request).await
        });
        handles.push(handle);
    }
    
    // Wait for all operations to complete
    let results = futures::future::join_all(handles).await;
    
    let duration = start_time.elapsed();
    
    // Count successful operations
    let success_count = results.iter()
        .filter(|r| r.is_ok() && r.as_ref().unwrap().is_ok())
        .count();
    
    // Enterprise requirements: Should handle 1000 operations efficiently
    assert!(success_count >= ENTERPRISE_LOAD_USERS * 95 / 100, 
           "Should have at least 95% success rate, got {}/{}", 
           success_count, ENTERPRISE_LOAD_USERS);
    
    // Should complete within reasonable time for enterprise load
    assert!(duration < Duration::from_secs(30), 
           "Load test took too long: {:?}", duration);
    
    println!("Enterprise load test: {} operations in {:?} ({:.2} ops/sec)", 
             success_count, duration, success_count as f64 / duration.as_secs_f64());
}

#[tokio::test]
async fn test_connection_recovery() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    // Test that connections can be recovered after temporary issues
    let pool = db_manager.pool();
    
    // Simulate connection usage
    for i in 0..10 {
        let result = sqlx::query_scalar::<_, i32>("SELECT $1")
            .bind(i)
            .fetch_one(pool)
            .await;
        
        assert!(result.is_ok(), "Query {} should succeed", i);
        assert_eq!(result.unwrap(), i);
    }
    
    // Test health check after usage
    db_manager.health_check().await
        .expect("Health check should pass after connection usage");
}

#[tokio::test]
async fn test_transaction_handling() {
    let db_manager = DatabaseManager::new(TEST_DATABASE_URL)
        .await
        .expect("Failed to initialize database manager");
    
    let pool = db_manager.pool();
    
    // Test successful transaction
    let mut tx = pool.begin().await
        .expect("Transaction should start");
    
    let user_id = Uuid::new_v4();
    
    sqlx::query(
        "INSERT INTO users (id, username, email, password_hash) VALUES ($1, $2, $3, $4)"
    )
    .bind(user_id)
    .bind("tx_test_user")
    .bind("<EMAIL>")
    .bind("hash123")
    .execute(&mut *tx)
    .await
    .expect("Insert should succeed");
    
    tx.commit().await
        .expect("Transaction should commit");
    
    // Verify user was created
    let user_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)"
    )
    .bind(user_id)
    .fetch_one(pool)
    .await
    .expect("Existence check should succeed");
    
    assert!(user_exists, "User should exist after transaction commit");
    
    // Test transaction rollback
    let mut tx = pool.begin().await
        .expect("Transaction should start");
    
    let rollback_user_id = Uuid::new_v4();
    
    sqlx::query(
        "INSERT INTO users (id, username, email, password_hash) VALUES ($1, $2, $3, $4)"
    )
    .bind(rollback_user_id)
    .bind("rollback_test_user")
    .bind("<EMAIL>")
    .bind("hash123")
    .execute(&mut *tx)
    .await
    .expect("Insert should succeed");
    
    tx.rollback().await
        .expect("Transaction should rollback");
    
    // Verify user was not created
    let user_exists: bool = sqlx::query_scalar(
        "SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)"
    )
    .bind(rollback_user_id)
    .fetch_one(pool)
    .await
    .expect("Existence check should succeed");
    
    assert!(!user_exists, "User should not exist after transaction rollback");
}

// Helper function to clean up test data
async fn cleanup_test_data(pool: &PgPool) -> ServiceResult<()> {
    sqlx::query("DELETE FROM users WHERE username LIKE '%test%'")
        .execute(pool)
        .await?;
    
    sqlx::query("DELETE FROM audit_events WHERE event_type LIKE '%test%'")
        .execute(pool)
        .await?;
    
    sqlx::query("DELETE FROM performance_metrics WHERE metric_name LIKE '%test%'")
        .execute(pool)
        .await?;
    
    Ok(())
}
