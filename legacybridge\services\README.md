# LegacyBridge Microservices Architecture

This directory contains the microservices implementation for LegacyBridge Phase 3 Architecture Improvements.

## Service Architecture Overview

The LegacyBridge application has been decomposed into the following independent microservices:

### Core Services

1. **Authentication Service** (`auth-service/`)
   - Port: 3001
   - Responsibilities: User authentication, JWT token management, user management
   - Database: PostgreSQL
   - Technology: Rust + Axum

2. **Conversion Service** (`conversion-service/`)
   - Port: 3002
   - Responsibilities: Document conversion, format detection, batch processing
   - Queue: Redis
   - Technology: Rust + Axum

3. **File Management Service** (`file-service/`)
   - Port: 3003
   - Responsibilities: File upload/download, metadata management, S3 integration
   - Storage: AWS S3 compatible
   - Technology: Rust + Axum

4. **Job Processing Service** (`job-service/`)
   - Port: 3004
   - Responsibilities: Background job processing, workflow management
   - Queue: Redis
   - Technology: Rust + Axum

### Infrastructure Components

5. **API Gateway** (`gateway/`)
   - Technology: Kong
   - Responsibilities: Request routing, authentication, rate limiting, CORS

6. **Shared Libraries** (`shared/`)
   - Common types, utilities, and communication patterns
   - Database repositories and connection management
   - Event system and circuit breakers

### Data Layer

- **PostgreSQL**: Primary database for user data, metadata, audit trails
- **Redis**: Caching, session storage, job queues, pub/sub events
- **S3**: Object storage for files

## Service Communication

### Synchronous Communication
- REST APIs for real-time operations
- Service-to-service HTTP calls with circuit breakers

### Asynchronous Communication
- Redis pub/sub for events
- Job queues for background processing

## Deployment

Each service is independently deployable with:
- Docker containers
- Kubernetes manifests
- Health checks and monitoring
- Auto-scaling configuration

## Development

### Prerequisites
- Rust 1.70+
- PostgreSQL 14+
- Redis 6+
- Docker & Docker Compose

### Quick Start
```bash
# Start infrastructure
docker-compose up -d postgres redis

# Run all services
./scripts/start-all-services.sh

# Or run individual services
cd auth-service && cargo run
cd conversion-service && cargo run
cd file-service && cargo run
```

### Testing
```bash
# Run all service tests
./scripts/test-all-services.sh

# Run integration tests
./scripts/test-integration.sh
```

## Monitoring

- Prometheus metrics collection
- Jaeger distributed tracing
- ELK stack for logging
- Grafana dashboards

## Security

- JWT-based authentication
- Service-to-service authentication
- Rate limiting and CORS
- Input validation and sanitization
