// Health check handlers
use axum::{
    extract::Extension,
    response::<PERSON><PERSON> as Response<PERSON><PERSON>,
};
use legacybridge_shared::{
    types::{ApiResponse, HealthResponse, ServiceMetrics as MetricsResponse},
    ServiceResult,
};
use serde_json::json;
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use tracing::{error, warn};

use crate::AppState;

/// Basic health check endpoint
pub async fn health_check(
    Extension(state): Extension<AppState>,
) -> ServiceResult<ResponseJson<ApiResponse<HealthResponse>>> {
    let uptime = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    let mut dependencies = HashMap::new();

    // Check database health
    match state.db.health_check().await {
        Ok(_) => {
            dependencies.insert("database".to_string(), "healthy".to_string());
        }
        Err(e) => {
            error!(error = %e, "Database health check failed");
            dependencies.insert("database".to_string(), "unhealthy".to_string());
        }
    }

    // Check Redis health
    match state.cache.health_check().await {
        Ok(_) => {
            dependencies.insert("redis".to_string(), "healthy".to_string());
        }
        Err(e) => {
            error!(error = %e, "Redis health check failed");
            dependencies.insert("redis".to_string(), "unhealthy".to_string());
        }
    }

    let health = HealthResponse {
        status: if dependencies.values().all(|status| status == "healthy") {
            "healthy".to_string()
        } else {
            "degraded".to_string()
        },
        version: state.config.service.version.clone(),
        uptime,
        dependencies,
    };

    Ok(ResponseJson(ApiResponse::success(health)))
}

/// Readiness check endpoint (for Kubernetes)
pub async fn readiness_check(
    Extension(state): Extension<AppState>,
) -> ServiceResult<ResponseJson<serde_json::Value>> {
    // Check if all critical dependencies are available
    let mut ready = true;
    let mut checks = HashMap::new();

    // Database check
    match state.db.health_check().await {
        Ok(_) => {
            checks.insert("database", "ready");
        }
        Err(e) => {
            warn!(error = %e, "Database not ready");
            checks.insert("database", "not_ready");
            ready = false;
        }
    }

    // Redis check
    match state.cache.health_check().await {
        Ok(_) => {
            checks.insert("redis", "ready");
        }
        Err(e) => {
            warn!(error = %e, "Redis not ready");
            checks.insert("redis", "not_ready");
            ready = false;
        }
    }

    let response = json!({
        "ready": ready,
        "checks": checks,
        "timestamp": chrono::Utc::now()
    });

    Ok(ResponseJson(response))
}

/// Detailed status endpoint with metrics
pub async fn status(
    Extension(state): Extension<AppState>,
) -> ServiceResult<ResponseJson<serde_json::Value>> {
    let uptime = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    // Get cache statistics
    let cache_stats = match state.cache.get_stats().await {
        Ok(stats) => Some(stats),
        Err(e) => {
            warn!(error = %e, "Failed to get cache stats");
            None
        }
    };

    // Get system metrics
    state.metrics.update_system_metrics();

    let response = json!({
        "service": {
            "name": state.config.service.name,
            "version": state.config.service.version,
            "environment": state.config.service.environment,
            "uptime_seconds": uptime
        },
        "health": {
            "database": match state.db.health_check().await {
                Ok(_) => "healthy",
                Err(_) => "unhealthy"
            },
            "redis": match state.cache.health_check().await {
                Ok(_) => "healthy",
                Err(_) => "unhealthy"
            }
        },
        "metrics": {
            "requests_total": state.metrics.requests_total.get(),
            "errors_total": state.metrics.errors_total.get(),
            "auth_events_total": state.metrics.auth_events_total.get(),
            "auth_failures_total": state.metrics.auth_failures_total.get(),
            "database_connections_active": state.metrics.database_connections_active.get(),
            "redis_connections_active": state.metrics.redis_connections_active.get(),
            "memory_usage_bytes": state.metrics.memory_usage_bytes.get(),
            "cpu_usage_percent": state.metrics.cpu_usage_percent.get()
        },
        "cache": cache_stats,
        "timestamp": chrono::Utc::now()
    });

    Ok(ResponseJson(response))
}
